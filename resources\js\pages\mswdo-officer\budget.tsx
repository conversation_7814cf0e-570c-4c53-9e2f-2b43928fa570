import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { stats } from '@/pages/mswdo-officer/tempData/budget';
import { monthlyTrends, barangayDistribution } from '@/pages/mswdo-officer/tempData/reports';
import { StatCardsSkeleton, TableRowSkeleton } from '@/components/skeletons/shared-skeletons';
import { Suspense } from 'react';
import { type BreadcrumbItem } from "@/types";

interface MonthlyTrend {
    month: string;
    applications: number;
    approved: number;
    amount: number;
}

interface BarangayData {
    barangay: string;
    applications: number;
    approved: number;
    total_amount: number;
}

export default function BudgetAllocation() {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: "Dashboard", href: "/superadmin/dashboard" },
        { title: "Budget Allocation", href: "/superadmin/budget" },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Budget Allocation" />
            <div className="p-6 space-y-6">
                {/* Overview Stats */}
                <Suspense fallback={<StatCardsSkeleton />}>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Total Budget</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">₱{(stats.totalBudget / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-muted-foreground">Annual budget</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Allocated</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">₱{(stats.totalAllocated / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-muted-foreground">{((stats.totalAllocated / stats.totalBudget) * 100).toFixed(1)}% of total</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Monthly Average</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">₱{(stats.monthlyAverage / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-muted-foreground">{stats.utilizationRate}% utilization</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Remaining</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">₱{(stats.totalRemaining / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-muted-foreground">Available funds</p>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                <Tabs defaultValue="monthly" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="monthly">Monthly Trends</TabsTrigger>
                        <TabsTrigger value="barangay">Barangay Distribution</TabsTrigger>
                    </TabsList>

                    <TabsContent value="monthly">
                        <Card>
                            <CardHeader>
                                <CardTitle>Monthly Budget Trends</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-4">
                                        {[...Array(4)].map((_, i) => (
                                            <TableRowSkeleton key={i} />
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-4">
                                        {monthlyTrends.map((month: MonthlyTrend) => (
                                            <div key={month.month} className="flex items-center justify-between">
                                                <div>
                                                    <p className="font-medium">{month.month}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {month.applications} applications
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">₱{(month.amount / 1000).toFixed(1)}K</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {month.approved} approved
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="barangay">
                        <Card>
                            <CardHeader>
                                <CardTitle>Budget by Barangay</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-4">
                                        {[...Array(9)].map((_, i) => (
                                            <TableRowSkeleton key={i} />
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-4">
                                        {barangayDistribution.map((barangay: BarangayData) => (
                                            <div key={barangay.barangay} className="flex items-center justify-between">
                                                <div>
                                                    <p className="font-medium">{barangay.barangay}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {barangay.applications} applications
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">₱{(barangay.total_amount / 1000).toFixed(1)}K</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {barangay.approved} approved
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
} 