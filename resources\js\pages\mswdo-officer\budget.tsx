import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Button } from '@/components/ui/button';
import { stats } from '@/pages/mswdo-officer/tempData/budget';
import { monthlyTrends, barangayDistribution } from '@/pages/mswdo-officer/tempData/reports';
import { StatCardsSkeleton, TableRowSkeleton } from '@/components/skeletons/shared-skeletons';
import { Suspense } from 'react';
import { type BreadcrumbItem } from "@/types";

interface MonthlyTrend {
    month: string;
    applications: number;
    approved: number;
    amount: number;
}

interface BarangayData {
    barangay: string;
    applications: number;
    approved: number;
    total_amount: number;
}

export default function BudgetAllocation() {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: "Dashboard", href: "/mswdo-officer/dashboard" },
        { title: "Budget Allocation", href: "/mswdo-officer/budget" },
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Budget Allocation" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Budget Allocation</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Manage and track social welfare budget allocation</p>
                    </div>
                </div>
                {/* Overview Stats */}
                <Suspense fallback={<StatCardsSkeleton />}>
                    <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-purple-900">Total Budget</CardTitle>
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <svg className="h-5 w-5 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                    </svg>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-purple-900">₱{(stats.totalBudget / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-purple-600">Annual budget</p>
                            </CardContent>
                        </Card>

                        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-blue-900">Allocated</CardTitle>
                                <div className="p-2 bg-blue-100 rounded-lg">
                                    <svg className="h-5 w-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                                    </svg>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-blue-900">₱{(stats.totalAllocated / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-blue-600">{((stats.totalAllocated / stats.totalBudget) * 100).toFixed(1)}% of total</p>
                            </CardContent>
                        </Card>

                        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-green-900">Monthly Average</CardTitle>
                                <div className="p-2 bg-green-100 rounded-lg">
                                    <svg className="h-5 w-5 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z" />
                                    </svg>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-green-900">₱{(stats.monthlyAverage / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-green-600">{stats.utilizationRate}% utilization</p>
                            </CardContent>
                        </Card>

                        <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-orange-900">Remaining</CardTitle>
                                <div className="p-2 bg-orange-100 rounded-lg">
                                    <svg className="h-5 w-5 text-orange-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 12H4" />
                                    </svg>
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-orange-900">₱{(stats.totalRemaining / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-orange-600">Available funds</p>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                <Tabs defaultValue="monthly" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="monthly">Monthly Trends</TabsTrigger>
                        <TabsTrigger value="barangay">Barangay Distribution</TabsTrigger>
                    </TabsList>

                    <TabsContent value="monthly">
                        <Card>
                            <CardHeader>
                                <CardTitle>Monthly Budget Trends</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-4">
                                        {[...Array(4)].map((_, i) => (
                                            <TableRowSkeleton key={i} />
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-4">
                                        {monthlyTrends.map((month: MonthlyTrend) => (
                                            <div key={month.month} className="flex items-center justify-between">
                                                <div>
                                                    <p className="font-medium">{month.month}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {month.applications} applications
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">₱{(month.amount / 1000).toFixed(1)}K</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {month.approved} approved
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="barangay">
                        <Card>
                            <CardHeader>
                                <CardTitle>Budget by Barangay</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-4">
                                        {[...Array(9)].map((_, i) => (
                                            <TableRowSkeleton key={i} />
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-4">
                                        {barangayDistribution.map((barangay: BarangayData) => (
                                            <div key={barangay.barangay} className="flex items-center justify-between">
                                                <div>
                                                    <p className="font-medium">{barangay.barangay}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {barangay.applications} applications
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">₱{(barangay.total_amount / 1000).toFixed(1)}K</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {barangay.approved} approved
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
} 