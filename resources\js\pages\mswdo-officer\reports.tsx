import { Head } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { servicePerformance, serviceCategories, workloadDistribution, barangayDistribution, monthlyTrends, stats } from '@/pages/mswdo-officer/tempData/reports';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Skeleton } from '@/components/ui/skeleton';
import { Suspense, lazy } from 'react';
import { type BreadcrumbItem } from "@/types";

// Lazy load ApexCharts
const ReactApexChart = lazy(() => import('react-apexcharts'));

// Loading skeleton for charts
const ChartSkeleton = () => (
    <div className="space-y-3">
        <Skeleton className="h-[400px] w-full" />
    </div>
);

// Loading skeleton for stats
const StatsSkeleton = () => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
            <Card key={i}>
                <CardHeader>
                    <Skeleton className="h-4 w-[120px]" />
                </CardHeader>
                <CardContent>
                    <Skeleton className="h-7 w-[70px] mb-1" />
                    <Skeleton className="h-4 w-[100px]" />
                </CardContent>
            </Card>
        ))}
    </div>
);

export default function ReportsAnalytics() {
    const breadcrumbs: BreadcrumbItem[] = [
        { title: "Dashboard", href: "/superadmin/dashboard" },
        { title: "Reports & Analytics", href: "/superadmin/reports" },
    ];

    // Line chart configuration
    const lineChartOptions = {
        chart: {
            type: 'line' as const,
            height: 400,
            toolbar: {
                show: true,
                tools: {
                    download: true,
                    selection: true,
                    zoom: true,
                    zoomin: true,
                    zoomout: true,
                    pan: true,
                    reset: true
                }
            },
            animations: {
                enabled: true,
                speed: 800,
                animateGradually: {
                    enabled: true,
                    delay: 150
                }
            }
        },
        stroke: {
            curve: 'smooth',
            width: [3, 3],
            dashArray: [0, 0]
        },
        colors: ['#2563eb', '#16a34a'],
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 5,
            xaxis: {
                lines: {
                    show: true
                }
            }
        },
        xaxis: {
            categories: servicePerformance.map(item => item.month),
            labels: {
                style: {
                    colors: '#64748b'
                }
            }
        },
        yaxis: [
            {
                title: {
                    text: 'Total Applications',
                    style: {
                        color: '#2563eb'
                    }
                },
                labels: {
                    style: {
                        colors: '#64748b'
                    }
                }
            },
            {
                opposite: true,
                title: {
                    text: 'Success Rate (%)',
                    style: {
                        color: '#16a34a'
                    }
                },
                labels: {
                    style: {
                        colors: '#64748b'
                    }
                }
            }
        ],
        legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            markers: {
                size: 8,
                strokeWidth: 0,
                shape: 'circle' as const,
                offsetX: 0,
                offsetY: 0
            }
        },
        tooltip: {
            theme: 'light',
            shared: true,
            intersect: false,
            y: {
                formatter: (value: number, { seriesIndex }: { seriesIndex: number }) => {
                    return seriesIndex === 1 ? `${value}%` : value.toString();
                }
            }
        }
    };

    const lineChartSeries = [
        {
            name: 'Total Applications',
            data: servicePerformance.map(item => item.totalApplications)
        },
        {
            name: 'Success Rate (%)',
            data: servicePerformance.map(item => item.successRate)
        }
    ];

    // Bar chart configuration
    const barChartOptions = {
        chart: {
            type: 'bar' as const,
            height: 400,
            toolbar: {
                show: true
            },
            animations: {
                enabled: true,
                speed: 800,
                animateGradually: {
                    enabled: true,
                    delay: 150
                }
            }
        },
        plotOptions: {
            bar: {
                borderRadius: 4,
                columnWidth: '60%',
                dataLabels: {
                    position: 'top'
                }
            }
        },
        colors: ['#6366f1', '#f59e0b'],
        grid: {
            borderColor: '#e2e8f0',
            strokeDashArray: 5
        },
        dataLabels: {
            enabled: true,
            formatter: (val: number) => val.toFixed(1),
            offsetY: -20,
            style: {
                fontSize: '12px',
                colors: ['#64748b']
            }
        },
        xaxis: {
            categories: servicePerformance.map(item => item.month),
            labels: {
                style: {
                    colors: '#64748b'
                }
            }
        },
        yaxis: {
            title: {
                text: 'Days / Score',
                style: {
                    color: '#64748b'
                }
            },
            labels: {
                style: {
                    colors: '#64748b'
                }
            }
        },
        legend: {
            position: 'bottom',
            horizontalAlign: 'center',
            markers: {
                size: 8,
                strokeWidth: 0,
                shape: 'circle' as const,
                offsetX: 0,
                offsetY: 0
            }
        },
        tooltip: {
            theme: 'light',
            y: {
                formatter: (val: number) => val.toFixed(1)
            }
        }
    };

    const barChartSeries = [
        {
            name: 'Avg. Processing Days',
            data: servicePerformance.map(item => item.averageProcessingDays)
        },
        {
            name: 'Satisfaction Score',
            data: servicePerformance.map(item => item.satisfactionScore)
        }
    ];

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Reports & Analytics" />
            <div className="p-6 space-y-6">
                {/* Overview Stats */}
                <Suspense fallback={<StatsSkeleton />}>
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Total Applications</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.totalApplications}</div>
                                <p className="text-sm text-muted-foreground">
                                    {stats.totalApproved} approved
                                </p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Processing Time</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.averageProcessingDays} days</div>
                                <p className="text-sm text-muted-foreground">Average processing time</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Amount Disbursed</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">₱{(stats.totalAmountDisbursed / 1000000).toFixed(2)}M</div>
                                <p className="text-sm text-muted-foreground">Total disbursed amount</p>
                            </CardContent>
                        </Card>
                        <Card>
                            <CardHeader>
                                <CardTitle className="text-sm font-medium">Satisfaction Rate</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <div className="text-2xl font-bold">{stats.overallSatisfactionRate}%</div>
                                <p className="text-sm text-muted-foreground">Overall satisfaction</p>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                <Tabs defaultValue="performance" className="space-y-4">
                    <TabsList>
                        <TabsTrigger value="performance">Service Performance</TabsTrigger>
                        <TabsTrigger value="barangay">Barangay Distribution</TabsTrigger>
                        <TabsTrigger value="workload">Workload Distribution</TabsTrigger>
                    </TabsList>

                    <TabsContent value="performance">
                        <div className="grid gap-4 md:grid-cols-2">
                            <Card>
                                <CardHeader>
                                    <CardTitle>Monthly Applications & Success Rate</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-[400px]">
                                        <Suspense fallback={<ChartSkeleton />}>
                                            <ReactApexChart
                                                options={lineChartOptions}
                                                series={lineChartSeries}
                                                type="line"
                                                height={400}
                                            />
                                        </Suspense>
                                    </div>
                                </CardContent>
                            </Card>

                            <Card>
                                <CardHeader>
                                    <CardTitle>Processing Time & Satisfaction</CardTitle>
                                </CardHeader>
                                <CardContent>
                                    <div className="h-[400px]">
                                        <Suspense fallback={<ChartSkeleton />}>
                                            <ReactApexChart
                                                options={barChartOptions}
                                                series={barChartSeries}
                                                type="bar"
                                                height={400}
                                            />
                                        </Suspense>
                                    </div>
                                </CardContent>
                            </Card>
                        </div>
                    </TabsContent>

                    <TabsContent value="barangay">
                        <Card>
                            <CardHeader>
                                <CardTitle>Applications by Barangay</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-4">
                                        {[...Array(9)].map((_, i) => (
                                            <div key={i} className="flex items-center justify-between">
                                                <div className="space-y-2">
                                                    <Skeleton className="h-4 w-[150px]" />
                                                    <Skeleton className="h-4 w-[100px]" />
                                                </div>
                                                <div className="space-y-2">
                                                    <Skeleton className="h-4 w-[100px]" />
                                                    <Skeleton className="h-4 w-[80px]" />
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-4">
                                        {barangayDistribution.map((item) => (
                                            <div key={item.barangay} className="flex items-center justify-between">
                                                <div>
                                                    <p className="font-medium">{item.barangay}</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {item.approved} of {item.applications} approved
                                                    </p>
                                                </div>
                                                <div className="text-right">
                                                    <p className="font-medium">₱{(item.total_amount / 1000).toFixed(1)}K</p>
                                                    <p className="text-sm text-muted-foreground">
                                                        {((item.approved / item.applications) * 100).toFixed(1)}% success rate
                                                    </p>
                                                </div>
                                            </div>
                                        ))}
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="workload">
                        <Card>
                            <CardHeader>
                                <CardTitle>Staff Workload Distribution</CardTitle>
                            </CardHeader>
                            <CardContent>
                                <Suspense fallback={
                                    <div className="space-y-6">
                                        {[...Array(2)].map((_, groupIndex) => (
                                            <div key={groupIndex} className="space-y-4">
                                                <Skeleton className="h-5 w-[120px]" />
                                                {[...Array(4)].map((_, i) => (
                                                    <div key={i} className="flex items-center justify-between">
                                                        <div className="space-y-2">
                                                            <Skeleton className="h-4 w-[150px]" />
                                                            <Skeleton className="h-4 w-[100px]" />
                                                        </div>
                                                        <div className="space-y-2">
                                                            <Skeleton className="h-4 w-[100px]" />
                                                            <Skeleton className="h-4 w-[80px]" />
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        ))}
                                    </div>
                                }>
                                    <div className="space-y-6">
                                        <div className="space-y-4">
                                            <h3 className="font-medium">Social Workers</h3>
                                            {workloadDistribution.socialWorkers.map((worker) => (
                                                <div key={worker.name} className="flex items-center justify-between">
                                                    <div>
                                                        <p className="font-medium">{worker.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {worker.activeApplications} active cases
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-medium">{worker.completedThisMonth} completed</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {worker.averageProcessingTime} days avg.
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>

                                        <div className="space-y-4">
                                            <h3 className="font-medium">Departments</h3>
                                            {workloadDistribution.departments.map((dept) => (
                                                <div key={dept.name} className="flex items-center justify-between">
                                                    <div>
                                                        <p className="font-medium">{dept.name}</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {dept.staffCount} staff members
                                                        </p>
                                                    </div>
                                                    <div className="text-right">
                                                        <p className="font-medium">{dept.activeApplications} active</p>
                                                        <p className="text-sm text-muted-foreground">
                                                            {dept.completedThisMonth} completed this month
                                                        </p>
                                                    </div>
                                                </div>
                                            ))}
                                        </div>
                                    </div>
                                </Suspense>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
} 