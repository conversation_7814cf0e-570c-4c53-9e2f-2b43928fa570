import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Users, UserCheck, UserX, Eye, Edit, Mail, Phone } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'Staff Management', href: '/mswdo-officer/staff' },
];

// Sample staff data
const staff = [
    {
        id: 1,
        name: '<PERSON>',
        email: 'maria.gonza<PERSON>@balagtas.gov.ph',
        role: 'social-worker',
        position: 'Senior Social Worker',
        status: 'active',
        department: 'MSWDO',
        dateHired: '2022-01-15',
        lastLogin: '2024-03-18 09:30:00',
        activeCases: 12,
        completedCases: 45,
        contactNumber: '09123456789',
        specialization: ['Child Welfare', 'Family Counseling']
    },
    {
        id: 2,
        name: 'Juan Santos',
        email: '<EMAIL>',
        role: 'social-worker',
        position: 'Social Worker II',
        status: 'active',
        department: 'MSWDO',
        dateHired: '2023-03-10',
        lastLogin: '2024-03-17 14:15:00',
        activeCases: 8,
        completedCases: 23,
        contactNumber: '09987654321',
        specialization: ['Senior Care', 'PWD Services']
    },
    {
        id: 3,
        name: 'Ana Reyes',
        email: '<EMAIL>',
        role: 'social-worker',
        position: 'Social Worker I',
        status: 'on_leave',
        department: 'MSWDO',
        dateHired: '2023-08-20',
        lastLogin: '2024-03-10 11:45:00',
        activeCases: 5,
        completedCases: 12,
        contactNumber: '09456789123',
        specialization: ['Community Development', 'Youth Programs']
    }
];

const stats = {
    totalStaff: 15,
    activeStaff: 12,
    onLeave: 2,
    socialWorkers: 10,
    adminStaff: 3,
    fieldWorkers: 2,
    totalActiveCases: 89,
    avgCasesPerWorker: 7.4
};

export default function StaffManagement() {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
            case 'on_leave':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">On Leave</Badge>;
            case 'inactive':
                return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getRoleBadge = (role: string) => {
        switch (role) {
            case 'social-worker':
                return <Badge variant="outline" className="text-xs bg-blue-50 text-blue-700">Social Worker</Badge>;
            case 'admin':
                return <Badge variant="outline" className="text-xs bg-purple-50 text-purple-700">Admin Staff</Badge>;
            case 'field-worker':
                return <Badge variant="outline" className="text-xs bg-green-50 text-green-700">Field Worker</Badge>;
            default:
                return <Badge variant="outline" className="text-xs">{role}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Staff Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Staff Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Manage MSWDO staff and social workers</p>
                    </div>
                    <Button asChild className="bg-purple-600 hover:bg-purple-700">
                        <Link href="/mswdo-officer/staff/new">
                            <Plus className="h-4 w-4 mr-2" />
                            Add Staff Member
                        </Link>
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Staff</CardTitle>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Users className="h-5 w-5 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalStaff}</div>
                            <CardDescription className="text-purple-600">
                                {stats.activeStaff} active
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Social Workers</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.socialWorkers}</div>
                            <CardDescription className="text-blue-600">
                                Licensed professionals
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Active Cases</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.totalActiveCases}</div>
                            <CardDescription className="text-green-600">
                                {stats.avgCasesPerWorker} avg per worker
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">On Leave</CardTitle>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <UserX className="h-5 w-5 text-orange-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">{stats.onLeave}</div>
                            <CardDescription className="text-orange-600">
                                Currently unavailable
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search staff by name, email, or position..."
                            className="pl-10"
                        />
                    </div>
                    <Button variant="outline" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filter
                    </Button>
                </div>

                {/* Staff List */}
                <Tabs defaultValue="all" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="all">All Staff</TabsTrigger>
                        <TabsTrigger value="social-workers">Social Workers</TabsTrigger>
                        <TabsTrigger value="admin">Admin Staff</TabsTrigger>
                        <TabsTrigger value="field-workers">Field Workers</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all" className="space-y-4">
                        <div className="grid gap-4">
                            {staff.map((member) => (
                                <Card key={member.id} className="border-purple-200 hover:bg-purple-50/30 transition-colors">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <h3 className="font-semibold text-lg text-purple-900">{member.name}</h3>
                                                    {getStatusBadge(member.status)}
                                                    {getRoleBadge(member.role)}
                                                </div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-2">
                                                    <p><span className="font-medium">Position:</span> {member.position}</p>
                                                    <p><span className="font-medium">Department:</span> {member.department}</p>
                                                    <p className="flex items-center gap-1">
                                                        <Mail className="h-3 w-3" />
                                                        {member.email}
                                                    </p>
                                                    <p className="flex items-center gap-1">
                                                        <Phone className="h-3 w-3" />
                                                        {member.contactNumber}
                                                    </p>
                                                    <p><span className="font-medium">Date Hired:</span> {member.dateHired}</p>
                                                    <p><span className="font-medium">Last Login:</span> {member.lastLogin}</p>
                                                </div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-2">
                                                    <p><span className="font-medium">Active Cases:</span> {member.activeCases}</p>
                                                    <p><span className="font-medium">Completed Cases:</span> {member.completedCases}</p>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Specialization:</span> {member.specialization.join(', ')}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/staff/${member.id}`}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/staff/${member.id}/edit`}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </TabsContent>

                    <TabsContent value="social-workers">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Social Workers list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="admin">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Admin Staff list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="field-workers">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Field Workers list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
