import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Ta<PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Database as DatabaseIcon, Server, Shield, Trash2, RefreshCw, HardDrive, Download, Upload, Activity } from "lucide-react";
import { useState } from "react";
import { Suspense } from 'react';
import { StatCardsSkeleton, CardWithActionsSkeleton } from '@/components/skeletons/shared-skeletons';
import { databaseStats, backupHistory } from '@/pages/mswdo-officer/tempData/database';
import { Progress } from '@/components/ui/progress';
import { type BreadcrumbItem } from "@/types";

interface DatabaseStats {
  totalSize: string;
  totalTables: number;
  totalRecords: number;
  lastBackup: string;
  backupSize: string;
  uptime: string;
}

interface TableStats {
  name: string;
  size: string;
  records: number;
  lastModified: string;
}

// Dummy data for demonstration
const dummyDatabaseStats: DatabaseStats = {
  totalSize: "2.5 GB",
  totalTables: 25,
  totalRecords: 150000,
  lastBackup: "2024-03-20 02:00:00",
  backupSize: "1.8 GB",
  uptime: "15 days, 4 hours",
};

const dummyTableStats: TableStats[] = [
  {
    name: "users",
    size: "256 MB",
    records: 50000,
    lastModified: "2024-03-20 15:30:00",
  },
  {
    name: "cases",
    size: "512 MB",
    records: 75000,
    lastModified: "2024-03-20 16:45:00",
  },
  {
    name: "documents",
    size: "1.2 GB",
    records: 25000,
    lastModified: "2024-03-20 14:20:00",
  },
];

const breadcrumbs: BreadcrumbItem[] = [
  { title: "Dashboard", href: "/superadmin/dashboard" },
  { title: "Database Management", href: "/superadmin/database" },
];

const DatabaseOverviewSkeleton = () => (
  <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
    {[...Array(4)].map((_, i) => (
      <Card key={i} className="animate-pulse">
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <div className="h-4 w-24 bg-gray-200 rounded"></div>
        </CardHeader>
        <CardContent>
          <div className="h-6 w-16 bg-gray-200 rounded mb-2"></div>
          <div className="h-4 w-32 bg-gray-200 rounded"></div>
        </CardContent>
      </Card>
    ))}
  </div>
);

const BackupHistorySkeleton = () => (
  <div className="space-y-4">
    {[...Array(3)].map((_, i) => (
      <div key={i} className="flex items-center justify-between p-4 border rounded animate-pulse">
        <div className="space-y-2">
          <div className="h-4 w-48 bg-gray-200 rounded"></div>
          <div className="h-4 w-32 bg-gray-200 rounded"></div>
        </div>
        <div className="h-8 w-24 bg-gray-200 rounded"></div>
      </div>
    ))}
  </div>
);

export default function Database() {
  const [activeTab, setActiveTab] = useState("overview");
  const [stats, setStats] = useState<DatabaseStats>(dummyDatabaseStats);
  const [tables, setTables] = useState<TableStats[]>(dummyTableStats);

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="Database Management" />

      <div className="container py-6">
        <div className="flex justify-between items-center mb-6">
          <div>
            <h1 className="text-2xl font-bold">Database Management</h1>
            <p className="text-sm text-gray-500 mt-1">
              Monitor and manage database performance and maintenance
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" className="flex items-center gap-2">
              <RefreshCw className="h-4 w-4" />
              Refresh Stats
            </Button>
            <Button variant="default" className="flex items-center gap-2">
              <HardDrive className="h-4 w-4" />
              Backup Now
            </Button>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
          <TabsList className="w-full grid grid-cols-1 sm:grid-cols-3 gap-4">
            <TabsTrigger value="overview" className="flex items-center gap-2">
              <DatabaseIcon className="h-4 w-4" />
              Overview
            </TabsTrigger>
            <TabsTrigger value="tables" className="flex items-center gap-2">
              <Server className="h-4 w-4" />
              Tables
            </TabsTrigger>
            <TabsTrigger value="backups" className="flex items-center gap-2">
              <Shield className="h-4 w-4" />
              Backups
            </TabsTrigger>
          </TabsList>

          <TabsContent value="overview">
            <Suspense fallback={<DatabaseOverviewSkeleton />}>
              <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Storage Used</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold mb-2">{databaseStats.storageUsed}GB</div>
                    <Progress value={(databaseStats.storageUsed / databaseStats.totalStorage) * 100} />
                    <p className="text-xs text-muted-foreground mt-2">
                      of {databaseStats.totalStorage}GB total
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Database Size</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databaseStats.databaseSize}GB</div>
                    <p className="text-xs text-muted-foreground">
                      {databaseStats.totalTables} tables
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Server Status</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databaseStats.serverStatus}</div>
                    <p className="text-xs text-muted-foreground">
                      {databaseStats.uptime} uptime
                    </p>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                    <CardTitle className="text-sm font-medium">Active Connections</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-2xl font-bold">{databaseStats.activeConnections}</div>
                    <p className="text-xs text-muted-foreground">
                      of {databaseStats.maxConnections} max
                    </p>
                  </CardContent>
                </Card>
              </div>
            </Suspense>

            <Card className="mt-6 p-6">
              <h2 className="text-lg font-semibold mb-4">Performance Metrics</h2>
              <div className="space-y-4">
                <div>
                  <Label>Query Cache Size</Label>
                  <Input type="text" value="256 MB" readOnly />
                </div>
                <div>
                  <Label>Max Connections</Label>
                  <Input type="text" value="100" readOnly />
                </div>
                <div>
                  <Label>Buffer Pool Size</Label>
                  <Input type="text" value="1 GB" readOnly />
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="tables">
            <Card className="p-6">
              <div className="space-y-6">
                <div className="flex justify-between items-center">
                  <h2 className="text-lg font-semibold">Table Management</h2>
                  <Select defaultValue="size">
                    <SelectTrigger className="w-[180px]">
                      <SelectValue placeholder="Sort by" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="size">Sort by Size</SelectItem>
                      <SelectItem value="records">Sort by Records</SelectItem>
                      <SelectItem value="modified">Sort by Last Modified</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-4">
                  {tables.map((table) => (
                    <Card key={table.name} className="p-4">
                      <div className="flex items-center justify-between">
                        <div>
                          <h3 className="font-semibold">{table.name}</h3>
                          <p className="text-sm text-gray-500">
                            {table.size} • {table.records.toLocaleString()} records
                          </p>
                          <p className="text-xs text-gray-400">
                            Last modified: {new Date(table.lastModified).toLocaleString()}
                          </p>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Optimize
                          </Button>
                          <Button variant="outline" size="sm">
                            Analyze
                          </Button>
                        </div>
                      </div>
                    </Card>
                  ))}
                </div>
              </div>
            </Card>
          </TabsContent>

          <TabsContent value="backups">
            <Card className="p-6">
              <div className="space-y-6">
                <div>
                  <h2 className="text-lg font-semibold mb-4">Backup Settings</h2>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    <div>
                      <Label>Backup Schedule</Label>
                      <Select defaultValue="daily">
                        <SelectTrigger>
                          <SelectValue placeholder="Select schedule" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="hourly">Every Hour</SelectItem>
                          <SelectItem value="daily">Daily</SelectItem>
                          <SelectItem value="weekly">Weekly</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label>Retention Period (days)</Label>
                      <Input type="number" defaultValue={30} />
                    </div>
                    <div>
                      <Label>Backup Location</Label>
                      <Input type="text" value="/var/backups/database" readOnly />
                    </div>
                    <div>
                      <Label>Compression Level</Label>
                      <Select defaultValue="medium">
                        <SelectTrigger>
                          <SelectValue placeholder="Select compression" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="low">Low</SelectItem>
                          <SelectItem value="medium">Medium</SelectItem>
                          <SelectItem value="high">High</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </div>

                <div>
                  <h2 className="text-lg font-semibold mb-4">Recent Backups</h2>
                  <div className="space-y-4">
                    <Suspense fallback={<BackupHistorySkeleton />}>
                      <div className="space-y-4">
                        {backupHistory.map((backup, index) => (
                          <div key={index} className="flex items-center justify-between p-4 border rounded">
                            <div>
                              <p className="font-medium">{backup.filename}</p>
                              <p className="text-sm text-muted-foreground">
                                {backup.created} • {backup.size}
                              </p>
                            </div>
                            <Button variant="ghost" size="sm">
                              <Download className="h-4 w-4" />
                            </Button>
                          </div>
                        ))}
                      </div>
                    </Suspense>
                  </div>
                </div>
              </div>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
} 