import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, Users, UserCheck, UserX, Eye, Edit, FileText } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'BFACES Management', href: '/mswdo-officer/bfaces' },
];

// Sample beneficiary data
const beneficiaries = [
    {
        id: 1,
        name: '<PERSON>',
        age: 45,
        address: 'Barangay San Jose, Balagtas',
        category: 'Senior Citizen',
        status: 'active',
        services: ['Medical Assistance', 'Social Pension'],
        lastAssistance: '2024-03-15',
        totalAssistance: 25000,
        contactNumber: '09123456789'
    },
    {
        id: 2,
        name: 'Juan Dela Cruz',
        age: 35,
        address: 'Barangay Poblacion, Balagtas',
        category: 'PWD',
        status: 'active',
        services: ['Assistive Devices', 'Skills Training'],
        lastAssistance: '2024-03-10',
        totalAssistance: 15000,
        contactNumber: '09987654321'
    },
    {
        id: 3,
        name: 'Ana Rodriguez',
        age: 28,
        address: 'Barangay Longos, Balagtas',
        category: 'Solo Parent',
        status: 'pending',
        services: ['Educational Support'],
        lastAssistance: '2024-02-20',
        totalAssistance: 8000,
        contactNumber: '09456789123'
    }
];

const stats = {
    totalBeneficiaries: 1247,
    activeBeneficiaries: 1089,
    pendingApplications: 158,
    seniorCitizens: 456,
    pwd: 234,
    soloParents: 189,
    children: 368
};

export default function BeneficiaryManagement() {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'active':
                return <Badge variant="default" className="bg-green-100 text-green-800">Active</Badge>;
            case 'pending':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case 'inactive':
                return <Badge variant="outline" className="bg-gray-100 text-gray-800">Inactive</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Beneficiary Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">BFACES Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Barangay Family Assessment and Case Evaluation System</p>
                    </div>
                    <div className="flex gap-2">
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                            <Link href="/mswdo-officer/bfaces/heads/new">
                                <Plus className="h-4 w-4 mr-2" />
                                Register Head of Family
                            </Link>
                        </Button>
                        <Button asChild variant="outline" className="border-purple-200 text-purple-700 hover:bg-purple-50">
                            <Link href="/mswdo-officer/bfaces/verification">
                                <FileText className="h-4 w-4 mr-2" />
                                Document Verification
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* BFACES Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Registered Families</CardTitle>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <Users className="h-5 w-5 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">247</div>
                            <CardDescription className="text-purple-600">
                                189 approved families
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Heads of Family</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">247</div>
                            <CardDescription className="text-green-600">
                                189 with BFACES codes
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Family Members</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <UserCheck className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">892</div>
                            <CardDescription className="text-blue-600">
                                Linked family members
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Verification</CardTitle>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <UserX className="h-5 w-5 text-orange-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">58</div>
                            <CardDescription className="text-orange-600">
                                Documents under review
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search families, control codes, or names..."
                            className="pl-10"
                        />
                    </div>
                    <Button variant="outline" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filter
                    </Button>
                </div>

                {/* BFACES Registry */}
                <Tabs defaultValue="all" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="all">All Families</TabsTrigger>
                        <TabsTrigger value="heads">Heads of Family</TabsTrigger>
                        <TabsTrigger value="members">Family Members</TabsTrigger>
                        <TabsTrigger value="pending">Pending Verification</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all" className="space-y-4">
                        <div className="grid gap-4">
                            {beneficiaries.map((beneficiary) => (
                                <Card key={beneficiary.id} className="border-purple-200 hover:bg-purple-50/30 transition-colors">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <h3 className="font-semibold text-lg text-purple-900">{beneficiary.name}</h3>
                                                    {getStatusBadge(beneficiary.status)}
                                                    <Badge variant="outline" className="text-xs">
                                                        {beneficiary.category}
                                                    </Badge>
                                                </div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600">
                                                    <p><span className="font-medium">Age:</span> {beneficiary.age}</p>
                                                    <p><span className="font-medium">Contact:</span> {beneficiary.contactNumber}</p>
                                                    <p><span className="font-medium">Address:</span> {beneficiary.address}</p>
                                                    <p><span className="font-medium">Total Assistance:</span> ₱{beneficiary.totalAssistance.toLocaleString()}</p>
                                                </div>
                                                <div className="mt-2">
                                                    <p className="text-sm text-gray-600">
                                                        <span className="font-medium">Services:</span> {beneficiary.services.join(', ')}
                                                    </p>
                                                </div>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/beneficiaries/${beneficiary.id}`}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/beneficiaries/${beneficiary.id}/edit`}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/new?beneficiary=${beneficiary.id}`}>
                                                        <FileText className="h-4 w-4 mr-2" />
                                                        New Case
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </TabsContent>

                    <TabsContent value="senior">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Senior Citizens list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="pwd">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">PWD beneficiaries list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="solo-parent">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Solo Parents list will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
