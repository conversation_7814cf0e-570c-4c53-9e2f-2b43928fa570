# Balagtas SocialCare – System Process Flow

## Client-Facing Modules

### 1. Account & Profile Management
- Registration & Authentication
  - Account creation
  - Login & secure authentication
  - Password management
  - Profile information management
- Residency Verification
  - Document upload interface
  - Verification status tracking
  - Additional document submission
  - Rejection/approval notifications

### 2. Dashboard & Notifications
- Personal Dashboard
  - Application status overview
  - Upcoming appointments
  - Important notifications
  - Service history
- Notification Center
  - Status updates
  - Document requests
  - Appointment reminders
  - System announcements

### 3. BFACES Application
- Application Form
  - Pre-filled personal information
  - Household information input
  - Crisis situation description
  - Family member details
- Document Management
  - Supporting document upload
  - Document status tracking
  - Additional document requests
  - Document history

### 4. Service Management
- Service Directory
  - Available services catalog
  - Eligibility requirements
  - Required documents list
  - Service descriptions
- Service Application
  - Application forms
  - Document upload tools
  - Status tracking
  - Appointment scheduling

### 5. Interview & Appointment
- Appointment Management
  - View scheduled interviews
  - Appointment confirmation
  - Rescheduling requests
  - Calendar integration
- Preparation Resources
  - Required document checklist
  - Interview preparation guide
  - Location and contact information
  - FAQ and guidelines

### 6. Communication & Support
- Messaging System
  - Direct messaging with social workers
  - Inquiry submission
  - Response tracking
  - Message history
- Help & Support
  - FAQ section
  - How-to guides
  - Contact information
  - Feedback submission

## Layered Process Flow

### Layer 1: Residency Verification

Purpose: Confirm that the applicant is a legitimate resident of Balagtas.

#### Stage 1: Account Registration

- User signs up with the following:
  - Full Name (First, Middle, Last, Suffix)
  - Date of Birth
  - Sex at Birth
  - Full Address (House/Street/Barangay/City/Province)
  - Email & Mobile Number
  - Password

- Validations:
  - Required fields
  - Email format check
  - Password strength
  - Address dropdowns and standardization

#### Stage 2: Proof of Residency Upload

- User is prompted to upload any of the following:
  - Latest electricity bill
  - Water bill
  - Government-issued ID with Balagtas address

- Social worker manually verifies submission via dashboard.
- Account Status: Pending Verification → Verified Resident
- Unlocks basic dashboard access (BFACES application only)

---

### Layer 2: BFACES Application

Purpose: Formal application for emergency/crisis assistance.

#### Stage 3: Fill Out BFACES Form

- Auto-filled from sign-up info
- Additional fields:
  - Household income
  - Emergency/crisis situation
  - Number of family members
  - Upload supporting documents

- Status: Under Review

#### Stage 4: BFACES Verification

- Admin or social worker verifies submission.
- Once verified → Dashboard unlocks service request options

---

### Layer 3: Services Availment

Purpose: Enable access to specific social welfare services.

#### Stage 5: Select a Service to Avail

- Available Services (admin-managed):
  - Medical Assistance
  - Burial Assistance
  - Aid to Senior Citizens
  - PAO Certification
  - PhilHealth Certification
- Each service has its own:
  - Description
  - Requirements (uploadable documents)

#### Stage 6: Document Verification

- User uploads softcopies.
- Social worker:
  - Views submitted documents
  - Verifies accuracy/completeness
- Once approved:
  - System prompts user to submit physical (hardcopy) requirements
  - On-site interview is scheduled on same day of submission

#### Stage 7: Final Approval & Claiming

- Within 24 hours post-interview:
  - Social worker validates hardcopy
  - System status → "Approved for Claiming"
- User is notified via dashboard or email

#### Stage 8: Service Cooldown Enforcement

- After claiming the assistance:
  - User is automatically placed under a 12-month cooldown
  - They cannot avail another service during this period
  - They cannot avail services simultaneously
  - A client is allowed to apply for only one service at a time. 

---

## Roles & Capabilities

| Role           | Capabilities                                                                 |
|----------------|------------------------------------------------------------------------------|
| Applicant      | Register, Upload Proof, Apply for BFACES, Avail Services, Track Status      |
| Social Worker  | Review Proofs, Verify BFACES, Validate Documents, Interview, Approve Claims |
| Admin          | Manage Services, Define Requirements, Monitor All Applications              |

---

## Suggested Database Status Fields

| Field Name                  | Possible Values                                                                 |
|----------------------------|----------------------------------------------------------------------------------|
| residency_status           | pending, verified, rejected                                                     |
| bfaces_status              | unfilled, submitted, under_review, verified, rejected                           |
| service_application_status | none, pending, documents_verified, for_interview, approved, cooldown_enforced  |
