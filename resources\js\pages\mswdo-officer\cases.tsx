import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
    Search, Plus, Filter, Download, Edit, Trash2, MoreHorizontal,
    ArrowUpDown, FileSpreadsheet, Eye, Calendar
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'Case Management', href: '/mswdo-officer/cases' },
];

// Excel-like case data structure matching their current spreadsheet
const cases = [
    {
        id: 1,
        caseloadNo: 'BSC-001',
        date: '2024-03-15',
        names: 'Maria Santos',
        birthdate: '1979-05-12',
        age: 45,
        address: 'Barangay San Jose, Balagtas',
        contactNumber: '09123456789',
        typeOfViolation: 'Medical Assistance',
        dateReported: '2024-03-15',
        counselling: 'Initial Assessment',
        counsellin: 'Maria Gonzales',
        remarks: 'Emergency medical assistance for hospitalization'
    },
    {
        id: 2,
        caseloadNo: 'BSC-002',
        date: '2024-03-14',
        names: 'Juan Dela Cruz',
        birthdate: '1989-08-23',
        age: 35,
        address: 'Barangay Poblacion, Balagtas',
        contactNumber: '09987654321',
        typeOfViolation: 'Educational Support',
        dateReported: '2024-03-14',
        counselling: 'Under Review',
        counsellin: 'Ana Reyes',
        remarks: 'Educational assistance for children school supplies'
    },
    {
        id: 3,
        caseloadNo: 'BSC-003',
        date: '2024-03-10',
        names: 'Ana Rodriguez',
        birthdate: '1996-02-18',
        age: 28,
        address: 'Barangay Longos, Balagtas',
        contactNumber: '09456789123',
        typeOfViolation: 'Financial Aid',
        dateReported: '2024-03-10',
        counselling: 'Completed',
        counsellin: 'Juan Santos',
        remarks: 'Emergency financial assistance - case resolved'
    },
    {
        id: 4,
        caseloadNo: 'BSC-004',
        date: '2024-03-12',
        names: 'Pedro Reyes',
        birthdate: '1955-11-30',
        age: 68,
        address: 'Barangay Wawa, Balagtas',
        contactNumber: '09234567890',
        typeOfViolation: 'Senior Citizen Assistance',
        dateReported: '2024-03-12',
        counselling: 'In Progress',
        counsellin: 'Maria Gonzales',
        remarks: 'Monthly pension and medical support'
    },
    {
        id: 5,
        caseloadNo: 'BSC-005',
        date: '2024-03-16',
        names: 'Carmen Villanueva',
        birthdate: '1985-07-04',
        age: 39,
        address: 'Barangay Borol 1st, Balagtas',
        contactNumber: '09345678901',
        typeOfViolation: 'Housing Assistance',
        dateReported: '2024-03-16',
        counselling: 'Pending',
        counsellin: 'Ana Reyes',
        remarks: 'Temporary shelter assistance due to fire incident'
    }
];

// Simple statistics for Excel-like interface
const stats = {
    totalCases: cases.length,
    pendingCases: cases.filter(c => c.counselling === 'Pending').length,
    inProgressCases: cases.filter(c => c.counselling === 'In Progress' || c.counselling === 'Under Review').length,
    completedCases: cases.filter(c => c.counselling === 'Completed').length
};

// Simple case types for filtering (matching Excel data)
const caseTypes = [
    'Medical Assistance',
    'Educational Support',
    'Financial Aid',
    'Senior Citizen Assistance',
    'Housing Assistance',
    'Food Assistance',
    'Transportation Aid',
    'Burial Assistance'
];

// Simple counselling statuses (matching Excel terminology)
const counsellingStatuses = [
    'Pending',
    'Initial Assessment',
    'Under Review',
    'In Progress',
    'Completed'
];

export default function CaseManagement() {
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [sortField, setSortField] = useState<string>('');
    const [sortDirection, setSortDirection] = useState<'asc' | 'desc'>('asc');

    const getCounsellingBadge = (status: string) => {
        switch (status) {
            case 'Pending':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case 'Initial Assessment':
                return <Badge variant="outline" className="bg-blue-100 text-blue-800">Initial Assessment</Badge>;
            case 'Under Review':
                return <Badge variant="outline" className="bg-purple-100 text-purple-800">Under Review</Badge>;
            case 'In Progress':
                return <Badge variant="outline" className="bg-orange-100 text-orange-800">In Progress</Badge>;
            case 'Completed':
                return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            month: '2-digit',
            day: '2-digit',
            year: 'numeric'
        });
    };

    const calculateAge = (birthdate: string) => {
        const today = new Date();
        const birth = new Date(birthdate);
        let age = today.getFullYear() - birth.getFullYear();
        const monthDiff = today.getMonth() - birth.getMonth();
        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
            age--;
        }
        return age;
    };

    const filteredCases = cases.filter(caseItem => {
        const matchesSearch =
            caseItem.caseloadNo.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.names.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.typeOfViolation.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.address.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.remarks.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesStatus = statusFilter === 'all' || caseItem.counselling === statusFilter;
        const matchesType = typeFilter === 'all' || caseItem.typeOfViolation === typeFilter;

        return matchesSearch && matchesStatus && matchesType;
    });

    const sortedCases = [...filteredCases].sort((a, b) => {
        if (!sortField) return 0;

        const aValue = a[sortField as keyof typeof a];
        const bValue = b[sortField as keyof typeof b];

        if (aValue < bValue) return sortDirection === 'asc' ? -1 : 1;
        if (aValue > bValue) return sortDirection === 'asc' ? 1 : -1;
        return 0;
    });

    const handleSort = (field: string) => {
        if (sortField === field) {
            setSortDirection(sortDirection === 'asc' ? 'desc' : 'asc');
        } else {
            setSortField(field);
            setSortDirection('asc');
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Case Management" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Case Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Digital case tracking system</p>
                    </div>
                    <div className="flex gap-2">
                        <Button variant="outline" className="flex items-center gap-2">
                            <FileSpreadsheet className="h-4 w-4" />
                            Export to Excel
                        </Button>
                        <Button asChild className="bg-purple-600 hover:bg-purple-700">
                            <Link href="/mswdo-officer/cases/new">
                                <Plus className="h-4 w-4 mr-2" />
                                Add New Case
                            </Link>
                        </Button>
                    </div>
                </div>

                {/* Simple Summary Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Cases</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalCases}</div>
                            <p className="text-sm text-purple-600">All recorded cases</p>
                        </CardContent>
                    </Card>

                    <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-yellow-900">Pending</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-yellow-900">{stats.pendingCases}</div>
                            <p className="text-sm text-yellow-600">Awaiting action</p>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">In Progress</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.inProgressCases}</div>
                            <p className="text-sm text-blue-600">Currently active</p>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Completed</CardTitle>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.completedCases}</div>
                            <p className="text-sm text-green-600">Successfully resolved</p>
                        </CardContent>
                    </Card>
                </div>

                {/* Simple Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search cases..."
                            className="pl-10"
                            value={searchQuery}
                            onChange={(e) => setSearchQuery(e.target.value)}
                        />
                    </div>
                    <div className="flex gap-2">
                        <Select value={statusFilter} onValueChange={setStatusFilter}>
                            <SelectTrigger className="w-[140px]">
                                <SelectValue placeholder="All Status" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Status</SelectItem>
                                {counsellingStatuses.map(status => (
                                    <SelectItem key={status} value={status}>
                                        {status}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>

                        <Select value={typeFilter} onValueChange={setTypeFilter}>
                            <SelectTrigger className="w-[160px]">
                                <SelectValue placeholder="All Types" />
                            </SelectTrigger>
                            <SelectContent>
                                <SelectItem value="all">All Types</SelectItem>
                                {caseTypes.map(type => (
                                    <SelectItem key={type} value={type}>
                                        {type}
                                    </SelectItem>
                                ))}
                            </SelectContent>
                        </Select>
                    </div>
                </div>

                <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-600">
                        Showing {sortedCases.length} of {stats.totalCases} cases
                    </p>
                </div>

                {/* Excel-like Table */}
                <Card className="border-purple-200">
                    <CardContent className="p-0">
                        <div className="overflow-x-auto">
                            <Table>
                                <TableHeader>
                                    <TableRow className="bg-purple-50">
                                        <TableHead className="w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('caseloadNo')} className="h-auto p-0 font-semibold">
                                                Caseload No.
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('date')} className="h-auto p-0 font-semibold">
                                                Date
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[150px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('names')} className="h-auto p-0 font-semibold">
                                                Names
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('birthdate')} className="h-auto p-0 font-semibold">
                                                Birthdate
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[60px] font-semibold text-purple-900">Age</TableHead>
                                        <TableHead className="w-[200px] font-semibold text-purple-900">Address</TableHead>
                                        <TableHead className="w-[120px] font-semibold text-purple-900">Contact Number</TableHead>
                                        <TableHead className="w-[150px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('typeOfViolation')} className="h-auto p-0 font-semibold">
                                                Type of Violation
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[100px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('dateReported')} className="h-auto p-0 font-semibold">
                                                Date Reported
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[120px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('counselling')} className="h-auto p-0 font-semibold">
                                                Counselling
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[120px] font-semibold text-purple-900">
                                            <Button variant="ghost" onClick={() => handleSort('counsellin')} className="h-auto p-0 font-semibold">
                                                Counsellin
                                                <ArrowUpDown className="ml-1 h-3 w-3" />
                                            </Button>
                                        </TableHead>
                                        <TableHead className="w-[200px] font-semibold text-purple-900">Remarks</TableHead>
                                        <TableHead className="w-[100px] font-semibold text-purple-900">Actions</TableHead>
                                    </TableRow>
                                </TableHeader>
                                <TableBody>
                                    {sortedCases.map((caseItem) => (
                                        <TableRow key={caseItem.id} className="hover:bg-purple-50/50">
                                            <TableCell className="font-medium text-purple-900">{caseItem.caseloadNo}</TableCell>
                                            <TableCell>{formatDate(caseItem.date)}</TableCell>
                                            <TableCell className="font-medium">{caseItem.names}</TableCell>
                                            <TableCell>{formatDate(caseItem.birthdate)}</TableCell>
                                            <TableCell>{caseItem.age}</TableCell>
                                            <TableCell className="max-w-[200px] truncate" title={caseItem.address}>
                                                {caseItem.address}
                                            </TableCell>
                                            <TableCell>{caseItem.contactNumber}</TableCell>
                                            <TableCell>{caseItem.typeOfViolation}</TableCell>
                                            <TableCell>{formatDate(caseItem.dateReported)}</TableCell>
                                            <TableCell>
                                                {getCounsellingBadge(caseItem.counselling)}
                                            </TableCell>
                                            <TableCell>{caseItem.counsellin}</TableCell>
                                            <TableCell className="max-w-[200px] truncate" title={caseItem.remarks}>
                                                {caseItem.remarks}
                                            </TableCell>
                                            <TableCell>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="ghost" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/mswdo-officer/cases/${caseItem.id}`}>
                                                                <Eye className="h-4 w-4 mr-2" />
                                                                View
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem asChild>
                                                            <Link href={`/mswdo-officer/cases/${caseItem.id}/edit`}>
                                                                <Edit className="h-4 w-4 mr-2" />
                                                                Edit
                                                            </Link>
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <Download className="h-4 w-4 mr-2" />
                                                            Export
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Delete
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </TableCell>
                                        </TableRow>
                                    ))}
                                </TableBody>
                            </Table>
                        </div>

                        {sortedCases.length === 0 && (
                            <div className="p-12 text-center">
                                <div className="text-gray-400 mb-4">
                                    <Calendar className="h-12 w-12 mx-auto" />
                                </div>
                                <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
                                <p className="text-gray-500 mb-4">
                                    {searchQuery || statusFilter !== 'all' || typeFilter !== 'all'
                                        ? 'Try adjusting your search criteria or filters.'
                                        : 'Get started by adding your first case.'
                                    }
                                </p>
                                <Button asChild>
                                    <Link href="/mswdo-officer/cases/new">
                                        <Plus className="h-4 w-4 mr-2" />
                                        Add New Case
                                    </Link>
                                </Button>
                            </div>
                        )}
                    </CardContent>
                </Card>
            </div>
        </AppLayout>
    );
}
