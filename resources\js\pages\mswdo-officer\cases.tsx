import { <PERSON>, <PERSON> } from '@inertiajs/react';
import { useState } from 'react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import {
    Search, Plus, Filter, FileText, Clock, CheckCircle, AlertTriangle, Eye, Edit,
    MoreHorizontal, Download, Trash2, Calendar, User, MapPin, Phone, Mail,
    Paperclip, History, Bell, Archive, RefreshCw, FileDown, Printer
} from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'Case Management', href: '/mswdo-officer/cases' },
];

// Enhanced case data with comprehensive information
const cases = [
    {
        id: 'CASE-2024-001',
        caseNumber: 'BSC-2024-001',
        beneficiary: {
            name: 'Maria Santos',
            age: 45,
            address: 'Barangay San Jose, Balagtas',
            contactNumber: '09123456789',
            email: '<EMAIL>',
            beneficiaryId: 'BEN-001'
        },
        type: 'Medical Assistance',
        category: 'Emergency Medical',
        status: 'in_progress',
        priority: 'high',
        dateCreated: '2024-03-15',
        dateUpdated: '2024-03-18',
        targetResolution: '2024-03-25',
        assignedTo: {
            name: 'Maria Gonzales',
            id: 'SW-001',
            email: '<EMAIL>'
        },
        description: 'Emergency medical assistance for hospitalization due to pneumonia. Patient requires immediate financial support for medical bills.',
        amount: 15000,
        amountApproved: 12000,
        documents: [
            { name: 'Medical Certificate', type: 'pdf', uploadDate: '2024-03-15' },
            { name: 'Hospital Bill', type: 'pdf', uploadDate: '2024-03-16' },
            { name: 'Barangay Certificate', type: 'pdf', uploadDate: '2024-03-15' }
        ],
        timeline: [
            { date: '2024-03-15', action: 'Case created', user: 'Maria Gonzales', details: 'Initial case assessment completed' },
            { date: '2024-03-16', action: 'Documents uploaded', user: 'Maria Gonzales', details: 'Medical certificate and hospital bill uploaded' },
            { date: '2024-03-18', action: 'Case reviewed', user: 'Juan Santos', details: 'Case approved for ₱12,000 assistance' }
        ],
        notes: 'Beneficiary is a senior citizen with no other source of income. Family unable to cover medical expenses.',
        tags: ['Senior Citizen', 'Emergency', 'Medical'],
        followUpDate: '2024-03-22'
    },
    {
        id: 'CASE-2024-002',
        caseNumber: 'BSC-2024-002',
        beneficiary: {
            name: 'Juan Dela Cruz',
            age: 35,
            address: 'Barangay Poblacion, Balagtas',
            contactNumber: '09987654321',
            email: '<EMAIL>',
            beneficiaryId: 'BEN-002'
        },
        type: 'Educational Support',
        category: 'Educational Assistance',
        status: 'pending',
        priority: 'medium',
        dateCreated: '2024-03-14',
        dateUpdated: '2024-03-16',
        targetResolution: '2024-03-28',
        assignedTo: {
            name: 'Ana Reyes',
            id: 'SW-002',
            email: '<EMAIL>'
        },
        description: 'Educational assistance for children\'s school supplies and tuition fees for the upcoming semester.',
        amount: 8000,
        amountApproved: null,
        documents: [
            { name: 'School Enrollment Form', type: 'pdf', uploadDate: '2024-03-14' },
            { name: 'Income Certificate', type: 'pdf', uploadDate: '2024-03-15' }
        ],
        timeline: [
            { date: '2024-03-14', action: 'Case created', user: 'Ana Reyes', details: 'Educational assistance request submitted' },
            { date: '2024-03-15', action: 'Documents uploaded', user: 'Ana Reyes', details: 'School enrollment and income certificate uploaded' },
            { date: '2024-03-16', action: 'Under review', user: 'Ana Reyes', details: 'Case forwarded for approval' }
        ],
        notes: 'Single parent with 3 children. Lost job due to company closure.',
        tags: ['Solo Parent', 'Educational', 'Multiple Children'],
        followUpDate: '2024-03-20'
    },
    {
        id: 'CASE-2024-003',
        caseNumber: 'BSC-2024-003',
        beneficiary: {
            name: 'Ana Rodriguez',
            age: 28,
            address: 'Barangay Longos, Balagtas',
            contactNumber: '09456789123',
            email: '<EMAIL>',
            beneficiaryId: 'BEN-003'
        },
        type: 'Financial Aid',
        category: 'Emergency Financial',
        status: 'completed',
        priority: 'low',
        dateCreated: '2024-03-10',
        dateUpdated: '2024-03-15',
        targetResolution: '2024-03-20',
        dateCompleted: '2024-03-15',
        assignedTo: {
            name: 'Juan Santos',
            id: 'SW-003',
            email: '<EMAIL>'
        },
        description: 'Emergency financial assistance for family due to unexpected job loss and urgent household needs.',
        amount: 5000,
        amountApproved: 5000,
        amountDisbursed: 5000,
        documents: [
            { name: 'Termination Letter', type: 'pdf', uploadDate: '2024-03-10' },
            { name: 'Utility Bills', type: 'pdf', uploadDate: '2024-03-11' },
            { name: 'Barangay Indigency Certificate', type: 'pdf', uploadDate: '2024-03-10' },
            { name: 'Disbursement Receipt', type: 'pdf', uploadDate: '2024-03-15' }
        ],
        timeline: [
            { date: '2024-03-10', action: 'Case created', user: 'Juan Santos', details: 'Emergency financial assistance request' },
            { date: '2024-03-11', action: 'Documents uploaded', user: 'Juan Santos', details: 'All required documents submitted' },
            { date: '2024-03-12', action: 'Case approved', user: 'Maria Gonzales', details: 'Approved for full amount of ₱5,000' },
            { date: '2024-03-15', action: 'Case completed', user: 'Juan Santos', details: 'Assistance disbursed successfully' }
        ],
        notes: 'Case resolved successfully. Beneficiary expressed gratitude for prompt assistance.',
        tags: ['Emergency', 'Job Loss', 'Completed'],
        followUpDate: null,
        satisfactionRating: 5
    }
];

const stats = {
    totalCases: 156,
    activeCases: 89,
    pendingCases: 34,
    completedCases: 33,
    overdueCases: 8,
    highPriority: 12,
    mediumPriority: 45,
    lowPriority: 32,
    avgResolutionTime: 7.5,
    totalAmountDisbursed: 2450000,
    casesThisMonth: 23,
    satisfactionRate: 4.2
};

// Case types for filtering
const caseTypes = [
    'Medical Assistance',
    'Educational Support',
    'Financial Aid',
    'Housing Assistance',
    'Food Assistance',
    'Transportation Aid',
    'Burial Assistance',
    'Disaster Relief'
];

// Case statuses
const caseStatuses = [
    { value: 'pending', label: 'Pending', color: 'yellow' },
    { value: 'in_progress', label: 'In Progress', color: 'blue' },
    { value: 'under_review', label: 'Under Review', color: 'purple' },
    { value: 'approved', label: 'Approved', color: 'green' },
    { value: 'completed', label: 'Completed', color: 'green' },
    { value: 'rejected', label: 'Rejected', color: 'red' },
    { value: 'on_hold', label: 'On Hold', color: 'orange' }
];

export default function CaseManagement() {
    const [searchQuery, setSearchQuery] = useState('');
    const [statusFilter, setStatusFilter] = useState('all');
    const [typeFilter, setTypeFilter] = useState('all');
    const [priorityFilter, setPriorityFilter] = useState('all');
    const [dateRange, setDateRange] = useState('all');
    const [selectedCases, setSelectedCases] = useState<string[]>([]);
    const [viewMode, setViewMode] = useState<'grid' | 'list'>('list');

    const getStatusBadge = (status: string) => {
        const statusConfig = caseStatuses.find(s => s.value === status);
        if (!statusConfig) return <Badge variant="outline">{status}</Badge>;

        const colorClasses = {
            yellow: 'bg-yellow-100 text-yellow-800 border-yellow-200',
            blue: 'bg-blue-100 text-blue-800 border-blue-200',
            purple: 'bg-purple-100 text-purple-800 border-purple-200',
            green: 'bg-green-100 text-green-800 border-green-200',
            red: 'bg-red-100 text-red-800 border-red-200',
            orange: 'bg-orange-100 text-orange-800 border-orange-200'
        };

        return (
            <Badge variant="outline" className={colorClasses[statusConfig.color as keyof typeof colorClasses]}>
                {statusConfig.label}
            </Badge>
        );
    };

    const getPriorityBadge = (priority: string) => {
        switch (priority) {
            case 'high':
                return <Badge variant="destructive" className="text-xs">High</Badge>;
            case 'medium':
                return <Badge variant="secondary" className="text-xs bg-yellow-100 text-yellow-800">Medium</Badge>;
            case 'low':
                return <Badge variant="outline" className="text-xs">Low</Badge>;
            default:
                return <Badge variant="outline" className="text-xs">{priority}</Badge>;
        }
    };

    const formatDate = (dateString: string) => {
        return new Date(dateString).toLocaleDateString('en-US', {
            year: 'numeric',
            month: 'short',
            day: 'numeric'
        });
    };

    const formatCurrency = (amount: number) => {
        return new Intl.NumberFormat('en-PH', {
            style: 'currency',
            currency: 'PHP'
        }).format(amount);
    };

    const filteredCases = cases.filter(caseItem => {
        const matchesSearch =
            caseItem.caseNumber.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.beneficiary.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.type.toLowerCase().includes(searchQuery.toLowerCase()) ||
            caseItem.description.toLowerCase().includes(searchQuery.toLowerCase());

        const matchesStatus = statusFilter === 'all' || caseItem.status === statusFilter;
        const matchesType = typeFilter === 'all' || caseItem.type === typeFilter;
        const matchesPriority = priorityFilter === 'all' || caseItem.priority === priorityFilter;

        return matchesSearch && matchesStatus && matchesType && matchesPriority;
    });

    const handleSelectCase = (caseId: string) => {
        setSelectedCases(prev =>
            prev.includes(caseId)
                ? prev.filter(id => id !== caseId)
                : [...prev, caseId]
        );
    };

    const handleSelectAll = () => {
        setSelectedCases(
            selectedCases.length === filteredCases.length
                ? []
                : filteredCases.map(c => c.id)
        );
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Case Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Case Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Track and manage social welfare cases</p>
                    </div>
                    <Button asChild className="bg-purple-600 hover:bg-purple-700">
                        <Link href="/mswdo-officer/cases/new">
                            <Plus className="h-4 w-4 mr-2" />
                            New Case
                        </Link>
                    </Button>
                </div>

                {/* Enhanced Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-6">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Cases</CardTitle>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <FileText className="h-5 w-5 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalCases}</div>
                            <CardDescription className="text-purple-600">
                                +{stats.casesThisMonth} this month
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Active Cases</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Clock className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.activeCases}</div>
                            <CardDescription className="text-blue-600">
                                Currently in progress
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Cases</CardTitle>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <AlertTriangle className="h-5 w-5 text-orange-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">{stats.pendingCases}</div>
                            <CardDescription className="text-orange-600">
                                {stats.overdueCases} overdue
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Completed</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.completedCases}</div>
                            <CardDescription className="text-green-600">
                                {stats.avgResolutionTime} days avg
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-indigo-200 bg-gradient-to-br from-indigo-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-indigo-900">Amount Disbursed</CardTitle>
                            <div className="p-2 bg-indigo-100 rounded-lg">
                                <svg className="h-5 w-5 text-indigo-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                                </svg>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-indigo-900">₱{(stats.totalAmountDisbursed / 1000000).toFixed(1)}M</div>
                            <CardDescription className="text-indigo-600">
                                Total assistance
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-teal-200 bg-gradient-to-br from-teal-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-teal-900">Satisfaction</CardTitle>
                            <div className="p-2 bg-teal-100 rounded-lg">
                                <svg className="h-5 w-5 text-teal-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M14.828 14.828a4 4 0 01-5.656 0M9 10h.01M15 10h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z" />
                                </svg>
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-teal-900">{stats.satisfactionRate}/5</div>
                            <CardDescription className="text-teal-600">
                                Average rating
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Advanced Search and Filter */}
                <div className="space-y-4">
                    <div className="flex flex-col lg:flex-row gap-4">
                        <div className="relative flex-1">
                            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                            <Input
                                placeholder="Search by case ID, beneficiary name, type, or description..."
                                className="pl-10"
                                value={searchQuery}
                                onChange={(e) => setSearchQuery(e.target.value)}
                            />
                        </div>
                        <div className="flex flex-wrap gap-2">
                            <Select value={statusFilter} onValueChange={setStatusFilter}>
                                <SelectTrigger className="w-[140px]">
                                    <SelectValue placeholder="Status" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Status</SelectItem>
                                    {caseStatuses.map(status => (
                                        <SelectItem key={status.value} value={status.value}>
                                            {status.label}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={typeFilter} onValueChange={setTypeFilter}>
                                <SelectTrigger className="w-[160px]">
                                    <SelectValue placeholder="Type" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Types</SelectItem>
                                    {caseTypes.map(type => (
                                        <SelectItem key={type} value={type}>
                                            {type}
                                        </SelectItem>
                                    ))}
                                </SelectContent>
                            </Select>

                            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
                                <SelectTrigger className="w-[120px]">
                                    <SelectValue placeholder="Priority" />
                                </SelectTrigger>
                                <SelectContent>
                                    <SelectItem value="all">All Priority</SelectItem>
                                    <SelectItem value="high">High</SelectItem>
                                    <SelectItem value="medium">Medium</SelectItem>
                                    <SelectItem value="low">Low</SelectItem>
                                </SelectContent>
                            </Select>
                        </div>
                    </div>

                    {/* Action Bar */}
                    <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
                        <div className="flex items-center gap-4">
                            <p className="text-sm text-gray-600">
                                Showing {filteredCases.length} of {stats.totalCases} cases
                                {selectedCases.length > 0 && (
                                    <span className="ml-2 text-purple-600 font-medium">
                                        ({selectedCases.length} selected)
                                    </span>
                                )}
                            </p>
                        </div>

                        <div className="flex items-center gap-2">
                            {selectedCases.length > 0 && (
                                <DropdownMenu>
                                    <DropdownMenuTrigger asChild>
                                        <Button variant="outline" size="sm">
                                            Bulk Actions
                                        </Button>
                                    </DropdownMenuTrigger>
                                    <DropdownMenuContent>
                                        <DropdownMenuItem>
                                            <Archive className="h-4 w-4 mr-2" />
                                            Archive Selected
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                            <FileDown className="h-4 w-4 mr-2" />
                                            Export Selected
                                        </DropdownMenuItem>
                                        <DropdownMenuItem>
                                            <RefreshCw className="h-4 w-4 mr-2" />
                                            Update Status
                                        </DropdownMenuItem>
                                    </DropdownMenuContent>
                                </DropdownMenu>
                            )}

                            <DropdownMenu>
                                <DropdownMenuTrigger asChild>
                                    <Button variant="outline" size="sm">
                                        <Download className="h-4 w-4 mr-2" />
                                        Export
                                    </Button>
                                </DropdownMenuTrigger>
                                <DropdownMenuContent>
                                    <DropdownMenuItem>
                                        <FileDown className="h-4 w-4 mr-2" />
                                        Export as Excel
                                    </DropdownMenuItem>
                                    <DropdownMenuItem>
                                        <Printer className="h-4 w-4 mr-2" />
                                        Export as PDF
                                    </DropdownMenuItem>
                                </DropdownMenuContent>
                            </DropdownMenu>

                            <Button className="bg-purple-600 hover:bg-purple-700" asChild>
                                <Link href="/mswdo-officer/cases/new">
                                    <Plus className="h-4 w-4 mr-2" />
                                    New Case
                                </Link>
                            </Button>
                        </div>
                    </div>
                </div>

                {/* Cases List */}
                <Tabs defaultValue="all" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="all">All Cases</TabsTrigger>
                        <TabsTrigger value="pending">Pending</TabsTrigger>
                        <TabsTrigger value="in_progress">In Progress</TabsTrigger>
                        <TabsTrigger value="completed">Completed</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all" className="space-y-4">
                        <div className="grid gap-4">
                            {filteredCases.map((caseItem) => (
                                <Card key={caseItem.id} className="border-purple-200 hover:bg-purple-50/30 transition-colors">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-start justify-between gap-4">
                                            <div className="flex items-start gap-4 flex-1">
                                                <input
                                                    type="checkbox"
                                                    checked={selectedCases.includes(caseItem.id)}
                                                    onChange={() => handleSelectCase(caseItem.id)}
                                                    className="mt-1 h-4 w-4 text-purple-600 rounded border-gray-300 focus:ring-purple-500"
                                                />
                                                <div className="flex-1">
                                                    <div className="flex items-center gap-3 mb-3">
                                                        <h3 className="font-semibold text-lg text-purple-900">{caseItem.caseNumber}</h3>
                                                        {getStatusBadge(caseItem.status)}
                                                        {getPriorityBadge(caseItem.priority)}
                                                        {caseItem.tags.map(tag => (
                                                            <Badge key={tag} variant="outline" className="text-xs bg-gray-50">
                                                                {tag}
                                                            </Badge>
                                                        ))}
                                                    </div>

                                                    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-3">
                                                        <div className="space-y-2">
                                                            <div className="flex items-center gap-2 text-sm">
                                                                <User className="h-4 w-4 text-gray-400" />
                                                                <div>
                                                                    <p className="font-medium text-gray-900">{caseItem.beneficiary.name}</p>
                                                                    <p className="text-gray-500 text-xs">{caseItem.beneficiary.beneficiaryId}</p>
                                                                </div>
                                                            </div>
                                                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                                                <MapPin className="h-4 w-4 text-gray-400" />
                                                                <span>{caseItem.beneficiary.address}</span>
                                                            </div>
                                                            <div className="flex items-center gap-2 text-sm text-gray-600">
                                                                <Phone className="h-4 w-4 text-gray-400" />
                                                                <span>{caseItem.beneficiary.contactNumber}</span>
                                                            </div>
                                                        </div>

                                                        <div className="space-y-2">
                                                            <p className="text-sm"><span className="font-medium">Type:</span> {caseItem.type}</p>
                                                            <p className="text-sm"><span className="font-medium">Category:</span> {caseItem.category}</p>
                                                            <p className="text-sm"><span className="font-medium">Assigned to:</span> {caseItem.assignedTo.name}</p>
                                                            <div className="flex items-center gap-2 text-sm">
                                                                <Calendar className="h-4 w-4 text-gray-400" />
                                                                <span>Created: {formatDate(caseItem.dateCreated)}</span>
                                                            </div>
                                                        </div>

                                                        <div className="space-y-2">
                                                            <p className="text-sm">
                                                                <span className="font-medium">Amount Requested:</span> {formatCurrency(caseItem.amount)}
                                                            </p>
                                                            {caseItem.amountApproved && (
                                                                <p className="text-sm">
                                                                    <span className="font-medium">Amount Approved:</span> {formatCurrency(caseItem.amountApproved)}
                                                                </p>
                                                            )}
                                                            {caseItem.amountDisbursed && (
                                                                <p className="text-sm">
                                                                    <span className="font-medium">Amount Disbursed:</span> {formatCurrency(caseItem.amountDisbursed)}
                                                                </p>
                                                            )}
                                                            <div className="flex items-center gap-2 text-sm">
                                                                <Paperclip className="h-4 w-4 text-gray-400" />
                                                                <span>{caseItem.documents.length} document(s)</span>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <p className="text-sm text-gray-600 mb-3">
                                                        <span className="font-medium">Description:</span> {caseItem.description}
                                                    </p>

                                                    {caseItem.followUpDate && (
                                                        <div className="flex items-center gap-2 text-sm text-orange-600 bg-orange-50 px-3 py-1 rounded-md w-fit">
                                                            <Bell className="h-4 w-4" />
                                                            <span>Follow-up due: {formatDate(caseItem.followUpDate)}</span>
                                                        </div>
                                                    )}
                                                </div>
                                            </div>

                                            <div className="flex flex-col gap-2 min-w-[120px]">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/${caseItem.id}`}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View Details
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/${caseItem.id}/edit`}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit Case
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/${caseItem.id}/timeline`}>
                                                        <History className="h-4 w-4 mr-2" />
                                                        Timeline
                                                    </Link>
                                                </Button>
                                                <DropdownMenu>
                                                    <DropdownMenuTrigger asChild>
                                                        <Button variant="outline" size="sm">
                                                            <MoreHorizontal className="h-4 w-4" />
                                                        </Button>
                                                    </DropdownMenuTrigger>
                                                    <DropdownMenuContent align="end">
                                                        <DropdownMenuItem>
                                                            <Paperclip className="h-4 w-4 mr-2" />
                                                            Manage Documents
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <RefreshCw className="h-4 w-4 mr-2" />
                                                            Update Status
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem>
                                                            <FileDown className="h-4 w-4 mr-2" />
                                                            Export Case
                                                        </DropdownMenuItem>
                                                        <DropdownMenuItem className="text-red-600">
                                                            <Trash2 className="h-4 w-4 mr-2" />
                                                            Archive Case
                                                        </DropdownMenuItem>
                                                    </DropdownMenuContent>
                                                </DropdownMenu>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>

                        {filteredCases.length === 0 && (
                            <Card>
                                <CardContent className="p-12 text-center">
                                    <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                                    <h3 className="text-lg font-medium text-gray-900 mb-2">No cases found</h3>
                                    <p className="text-gray-500 mb-4">
                                        {searchQuery || statusFilter !== 'all' || typeFilter !== 'all' || priorityFilter !== 'all'
                                            ? 'Try adjusting your search criteria or filters.'
                                            : 'Get started by creating your first case.'
                                        }
                                    </p>
                                    <Button asChild>
                                        <Link href="/mswdo-officer/cases/new">
                                            <Plus className="h-4 w-4 mr-2" />
                                            Create New Case
                                        </Link>
                                    </Button>
                                </CardContent>
                            </Card>
                        )}
                    </TabsContent>

                    <TabsContent value="pending">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Pending cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="in_progress">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">In progress cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="completed">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Completed cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
