import { <PERSON>, <PERSON> } from '@inertiajs/react';
import AppLayout from '@/layouts/app-layout';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Search, Plus, Filter, FileText, Clock, CheckCircle, AlertTriangle, Eye, Edit } from 'lucide-react';
import { type BreadcrumbItem } from '@/types';

const breadcrumbs: BreadcrumbItem[] = [
    { label: 'Dashboard', href: '/mswdo-officer/dashboard' },
    { label: 'Case Management', href: '/mswdo-officer/cases' },
];

// Sample case data
const cases = [
    {
        id: 'CASE-2024-001',
        beneficiary: '<PERSON>',
        type: 'Medical Assistance',
        status: 'in_progress',
        priority: 'high',
        dateCreated: '2024-03-15',
        lastUpdate: '2024-03-18',
        assignedTo: 'Social Worker A',
        description: 'Emergency medical assistance for hospitalization',
        amount: 15000,
        documents: 3
    },
    {
        id: 'CASE-2024-002',
        beneficiary: 'Juan Dela Cruz',
        type: 'Educational Support',
        status: 'pending',
        priority: 'medium',
        dateCreated: '2024-03-14',
        lastUpdate: '2024-03-16',
        assignedTo: 'Social Worker B',
        description: 'School supplies and tuition assistance',
        amount: 8000,
        documents: 2
    },
    {
        id: 'CASE-2024-003',
        beneficiary: 'Ana Rodriguez',
        type: 'Financial Aid',
        status: 'completed',
        priority: 'low',
        dateCreated: '2024-03-10',
        lastUpdate: '2024-03-15',
        assignedTo: 'Social Worker C',
        description: 'Emergency financial assistance for family',
        amount: 5000,
        documents: 4
    }
];

const stats = {
    totalCases: 156,
    activeCases: 89,
    pendingCases: 34,
    completedCases: 33,
    highPriority: 12,
    mediumPriority: 45,
    lowPriority: 32
};

export default function CaseManagement() {
    const getStatusBadge = (status: string) => {
        switch (status) {
            case 'pending':
                return <Badge variant="secondary" className="bg-yellow-100 text-yellow-800">Pending</Badge>;
            case 'in_progress':
                return <Badge variant="default" className="bg-blue-100 text-blue-800">In Progress</Badge>;
            case 'completed':
                return <Badge variant="default" className="bg-green-100 text-green-800">Completed</Badge>;
            case 'cancelled':
                return <Badge variant="destructive" className="bg-red-100 text-red-800">Cancelled</Badge>;
            default:
                return <Badge variant="outline">{status}</Badge>;
        }
    };

    const getPriorityBadge = (priority: string) => {
        switch (priority) {
            case 'high':
                return <Badge variant="destructive" className="text-xs">High</Badge>;
            case 'medium':
                return <Badge variant="secondary" className="text-xs">Medium</Badge>;
            case 'low':
                return <Badge variant="outline" className="text-xs">Low</Badge>;
            default:
                return <Badge variant="outline" className="text-xs">{priority}</Badge>;
        }
    };

    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="Case Management" />
            
            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">Case Management</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Track and manage social welfare cases</p>
                    </div>
                    <Button asChild className="bg-purple-600 hover:bg-purple-700">
                        <Link href="/mswdo-officer/cases/new">
                            <Plus className="h-4 w-4 mr-2" />
                            New Case
                        </Link>
                    </Button>
                </div>

                {/* Statistics Cards */}
                <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-purple-900">Total Cases</CardTitle>
                            <div className="p-2 bg-purple-100 rounded-lg">
                                <FileText className="h-5 w-5 text-purple-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-purple-900">{stats.totalCases}</div>
                            <CardDescription className="text-purple-600">
                                All time cases
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-blue-900">Active Cases</CardTitle>
                            <div className="p-2 bg-blue-100 rounded-lg">
                                <Clock className="h-5 w-5 text-blue-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-blue-900">{stats.activeCases}</div>
                            <CardDescription className="text-blue-600">
                                Currently in progress
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-orange-900">Pending Cases</CardTitle>
                            <div className="p-2 bg-orange-100 rounded-lg">
                                <AlertTriangle className="h-5 w-5 text-orange-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-orange-900">{stats.pendingCases}</div>
                            <CardDescription className="text-orange-600">
                                Awaiting action
                            </CardDescription>
                        </CardContent>
                    </Card>

                    <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                        <CardHeader className="flex flex-row items-center justify-between pb-3">
                            <CardTitle className="text-sm font-medium text-green-900">Completed Cases</CardTitle>
                            <div className="p-2 bg-green-100 rounded-lg">
                                <CheckCircle className="h-5 w-5 text-green-600" />
                            </div>
                        </CardHeader>
                        <CardContent>
                            <div className="text-3xl font-bold text-green-900">{stats.completedCases}</div>
                            <CardDescription className="text-green-600">
                                Successfully resolved
                            </CardDescription>
                        </CardContent>
                    </Card>
                </div>

                {/* Search and Filter */}
                <div className="flex flex-col sm:flex-row gap-4">
                    <div className="relative flex-1">
                        <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                        <Input
                            placeholder="Search cases by ID, beneficiary, or type..."
                            className="pl-10"
                        />
                    </div>
                    <Button variant="outline" className="flex items-center gap-2">
                        <Filter className="h-4 w-4" />
                        Filter
                    </Button>
                </div>

                {/* Cases List */}
                <Tabs defaultValue="all" className="w-full">
                    <TabsList className="grid w-full grid-cols-4">
                        <TabsTrigger value="all">All Cases</TabsTrigger>
                        <TabsTrigger value="pending">Pending</TabsTrigger>
                        <TabsTrigger value="in_progress">In Progress</TabsTrigger>
                        <TabsTrigger value="completed">Completed</TabsTrigger>
                    </TabsList>

                    <TabsContent value="all" className="space-y-4">
                        <div className="grid gap-4">
                            {cases.map((caseItem) => (
                                <Card key={caseItem.id} className="border-purple-200 hover:bg-purple-50/30 transition-colors">
                                    <CardContent className="p-6">
                                        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
                                            <div className="flex-1">
                                                <div className="flex items-center gap-3 mb-2">
                                                    <h3 className="font-semibold text-lg text-purple-900">{caseItem.id}</h3>
                                                    {getStatusBadge(caseItem.status)}
                                                    {getPriorityBadge(caseItem.priority)}
                                                </div>
                                                <div className="grid grid-cols-1 md:grid-cols-2 gap-2 text-sm text-gray-600 mb-2">
                                                    <p><span className="font-medium">Beneficiary:</span> {caseItem.beneficiary}</p>
                                                    <p><span className="font-medium">Type:</span> {caseItem.type}</p>
                                                    <p><span className="font-medium">Assigned to:</span> {caseItem.assignedTo}</p>
                                                    <p><span className="font-medium">Amount:</span> ₱{caseItem.amount.toLocaleString()}</p>
                                                    <p><span className="font-medium">Created:</span> {caseItem.dateCreated}</p>
                                                    <p><span className="font-medium">Last Update:</span> {caseItem.lastUpdate}</p>
                                                </div>
                                                <p className="text-sm text-gray-600">
                                                    <span className="font-medium">Description:</span> {caseItem.description}
                                                </p>
                                                <p className="text-xs text-gray-500 mt-1">
                                                    {caseItem.documents} document(s) attached
                                                </p>
                                            </div>
                                            <div className="flex gap-2">
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/${caseItem.id}`}>
                                                        <Eye className="h-4 w-4 mr-2" />
                                                        View
                                                    </Link>
                                                </Button>
                                                <Button variant="outline" size="sm" asChild>
                                                    <Link href={`/mswdo-officer/cases/${caseItem.id}/edit`}>
                                                        <Edit className="h-4 w-4 mr-2" />
                                                        Edit
                                                    </Link>
                                                </Button>
                                            </div>
                                        </div>
                                    </CardContent>
                                </Card>
                            ))}
                        </div>
                    </TabsContent>

                    <TabsContent value="pending">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Pending cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="in_progress">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">In progress cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>

                    <TabsContent value="completed">
                        <Card>
                            <CardContent className="p-6">
                                <p className="text-center text-gray-500">Completed cases will be displayed here</p>
                            </CardContent>
                        </Card>
                    </TabsContent>
                </Tabs>
            </div>
        </AppLayout>
    );
}
