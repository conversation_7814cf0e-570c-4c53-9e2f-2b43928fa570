# Balagtas SocialCare – Admin (Social Workers) Modules

## Permission-Based Access System

Each admin account is granted specific permissions by the superadmin. Access to modules is determined by these individual permissions, not by role. An admin will only see and have access to the modules they have been explicitly granted permission to use.

Available permissions include:

1. Case Management
   - Access to case management module
   - View and manage cases
   - Update case status and details

2. Residency Verification
   - Access to residency verification module
   - Review and verify residency documents
   - Approve or reject verifications

3. BFACES Application Management
   - Access to BFACES application module
   - Process BFACES applications
   - Review and update application status

4. Service Request Management
   - Access to service request module
   - Process service requests
   - Track request status

5. Interview Management
   - Access to interview scheduling
   - Manage appointments
   - Conduct and record interviews

6. Client Management
   - Access to client records
   - Update client information
   - View service history

7. Reports and Analytics
   - Access to reporting tools
   - Generate and view reports
   - Analyze service data

The dashboard and navigation will dynamically adjust to show only the modules and features that the admin has permission to access. This ensures that admins can focus on their assigned responsibilities and maintain clear workflow boundaries.

## Decision Support System Modules

### 1. Case Management DSS
- Smart Case Assessment
  - AI-powered case analysis
  - Risk level prediction
  - Priority scoring
  - Service recommendation engine
- Decision Support Tools
  - Intervention suggestions
  - Similar case references
  - Best practice recommendations
  - Resource availability check
- Case Planning Assistant
  - Service package suggestions
  - Timeline recommendations
  - Resource requirement estimates
  - Success probability indicators
- Outcome Prediction
  - Case resolution forecasting
  - Required resource estimation
  - Timeline projections
  - Risk factor alerts

### 2. Financial Resource Utilization DSS
- Resource Availability
  - Real-time budget status
  - Service allocation limits
  - Emergency fund access
  - Resource utilization tracking
- Smart Assistance Planning
  - AI-suggested assistance amounts
  - Cost-effective service combinations
  - Resource optimization tips
  - Budget impact analysis
- Financial Decision Support
  - Case priority suggestions
  - Resource allocation recommendations
  - Alternative assistance options
  - Cost-benefit insights
- Budget Monitoring
  - Allocation tracking
  - Spending patterns
  - Resource availability alerts
  - Usage efficiency metrics

## Core Modules

### 1. Dashboard
- Quick access to pending tasks
- Recent activities
- Important notifications
- Daily schedule overview
- Key performance metrics

### 2. Residency Verification Module
- Dashboard for pending residency verifications
- Document review interface
  - View uploaded proof of residency documents
  - Zoom and rotate document viewer
  - Document comparison tools
- Verification actions
  - Approve/Reject with comments
  - Request additional documents
  - Flag suspicious documents
- Track verification history
- Generate verification reports

### 3. BFACES Application Management
- Application review dashboard
  - Pending applications queue
  - Priority cases highlight
  - Deadline tracking
- Document verification
  - Household income validation
  - Crisis situation assessment
  - Supporting document review
- Application processing
  - Status updates
  - Comment and note system
  - Approval/Rejection workflow
- Report generation
  - Application statistics
  - Processing time metrics
  - Outcome reports

### 4. Service Request Management
- Service application dashboard
  - Filter by service type:
    - Medical Assistance
    - Burial Assistance
    - Aid to Senior Citizens
    - PAO Certification
    - PhilHealth Certification
  - Sort by priority/deadline
- Document verification workflow
  - Checklist-based verification
  - Document completeness check
  - Validity period tracking
- Interview scheduling
  - Calendar integration
  - Automated notifications
  - Rescheduling management
- Application processing
  - Status tracking
  - Cooldown period monitoring
  - Service history view

### 5. Interview Management
- Interview scheduling system
  - Calendar view
  - Time slot management
  - Conflict detection
- Interview workspace
  - Client information summary
  - Document quick view
  - Note-taking interface
  - Assessment forms
- Interview tracking
  - Attendance recording
  - Outcome documentation
  - Follow-up scheduling
- Report generation
  - Interview summaries
  - Assessment outcomes
  - Recommendation reports

### 6. Client Management
- Client profiles
  - Personal information
  - Service history
  - Document repository
  - Communication log
- Service tracking
  - Active applications
  - Cooldown periods
  - Service eligibility
- Client communication
  - Notification management
  - Message templates
  - Contact history
- Report generation
  - Client statistics
  - Service utilization
  - Interaction history

## Additional Features

### 1. Calendar Management
- Personal schedule
- Interview appointments
- Team availability view
- Deadline tracking
- Event management

### 2. Communication Center
- Internal messaging
- Client notifications
- Email templates
- SMS notifications
- Communication history

### 3. Document Management
- Document upload/download
- File organization
- Version tracking
- Template access
- Scanning interface

### 4. Profile Management
- Personal information
- Account settings
- Notification preferences
- Activity history
- Performance metrics

### 5. Reports and Analytics
- Daily activity reports
- Case processing metrics
- Service delivery statistics
- Client interaction logs
- Custom report generation

## Task Management

### 1. Daily Tasks
- Task queue
- Priority management
- Deadline tracking
- Progress monitoring
- Task notes

### 2. Case Assignment
- Case distribution
- Workload management
- Backup assignments
- Transfer protocols

### 3. Collaboration Tools
- Team messaging
- Case sharing
- Document collaboration
- Knowledge base access

## Compliance and Quality

### 1. Process Compliance
- SLA monitoring
- Required field validation
- Document completeness
- Policy adherence

### 2. Quality Assurance
- Decision review process
- Document quality check
- Service delivery standards
- Client feedback tracking
