<?php

namespace App\Models;

// use Illuminate\Contracts\Auth\MustVerifyEmail;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Foundation\Auth\User as Authenticatable;
use Illuminate\Notifications\Notifiable;

class User extends Authenticatable
{
    /** @use HasFactory<\Database\Factories\UserFactory> */
    use HasFactory, Notifiable;

    /**
     * The attributes that are mass assignable.
     *
     * @var list<string>
     */
    protected $fillable = [
        'name',
        'email',
        'password',
        'role',
        'status',
        'phone'
    ];

    /**
     * The attributes that should be hidden for serialization.
     *
     * @var list<string>
     */
    protected $hidden = [
        'password',
        'remember_token',
    ];

    /**
     * Get the attributes that should be cast.
     *
     * @return array<string, string>
     */
    protected function casts(): array
    {
        return [
            'email_verified_at' => 'datetime',
            'password' => 'hashed',
        ];
    }

    public function profile()
    {
        return $this->hasOne(Profile::class);
    }

    public function adminProfile()
    {
        return $this->hasOne(AdminProfile::class);
    }

    public function isSocialWorker()
    {
        return $this->role === 'social-worker';
    }

    public function isMswdoOfficer()
    {
        return $this->role === 'mswdo-officer';
    }

    public function isApplicant()
    {
        return $this->role === 'applicant';
    }

    public function isVerified()
    {
        return $this->status === 'verified';
    }

    public function hasRole(string $role): bool
    {
        // Normalize the user's role to standard format
        $userRole = $this->normalizeRole($this->role);

        // Superadmin and admin have access to all roles
        if (in_array($userRole, ['superadmin', 'mswdo-officer'])) {
            return true;
        }

        if ($role === 'social-worker') {
            return in_array($userRole, ['social-worker', 'mswdo-officer']);
        }

        return $userRole === $role;
    }

    /**
     * Normalize role names to standard format
     */
    private function normalizeRole(?string $role): string
    {
        if (!$role) {
            return 'applicant';
        }

        // Map various role formats to standard names
        $roleMap = [
            'superadmin' => 'mswdo-officer',
            'admin' => 'mswdo-officer',
            'mswdo-officer' => 'mswdo-officer',
            'MSWDO Officer' => 'mswdo-officer',
            'social-worker' => 'social-worker',
            'Social Worker' => 'social-worker',
            'applicant' => 'applicant',
            'client' => 'applicant',
            'Applicant' => 'applicant',
        ];

        return $roleMap[trim($role)] ?? 'applicant';
    }

    /**
     * Get the normalized role for routing purposes
     */
    public function getNormalizedRole(): string
    {
        return $this->normalizeRole($this->role);
    }
}
