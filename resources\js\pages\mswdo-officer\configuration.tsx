import { Head } from "@inertiajs/react";
import AppLayout from "@/layouts/app/app-sidebar-layout";
import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Skeleton } from "@/components/ui/skeleton";
import { Mail, Bell, Shield, Server } from "lucide-react";
import { Suspense, useState } from "react";
import { config as defaultSystemConfig } from "@/pages/mswdo-officer/tempData/configuration";
import { FormSkeleton, TabsSkeleton } from "@/components/skeletons/shared-skeletons";
import { type BreadcrumbItem } from "@/types";

interface SystemConfig {
  emailSettings: {
    smtpServer: string;
    port: number;
    username: string;
    senderName: string;
    senderEmail: string;
  };
  notificationSettings: {
    enableEmail: boolean;
    enableSMS: boolean;
    enableInApp: boolean;
  };
  securitySettings: {
    sessionTimeout: number;
    maxLoginAttempts: number;
    passwordExpiry: number;
    requireTwoFactor: boolean;
  };
  maintenanceSettings: {
    backupTime: string;
    maintenanceWindow: string;
    retentionPeriod: number;
  };
}

export default function Configuration() {
  const [activeTab, setActiveTab] = useState("notifications");
  const [systemConfig, setSystemConfig] = useState<SystemConfig>(defaultSystemConfig);

  const breadcrumbs: BreadcrumbItem[] = [
    { title: "Dashboard", href: "/mswdo-officer/dashboard" },
    { title: "System Configuration", href: "/mswdo-officer/configuration" },
  ];

  const handleSystemConfigChange = (
    category: keyof SystemConfig,
    setting: string,
    value: string | number | boolean
  ) => {
    setSystemConfig(prev => ({
      ...prev,
      [category]: {
        ...prev[category],
        [setting]: value,
      },
    }));
  };

  return (
    <AppLayout breadcrumbs={breadcrumbs}>
      <Head title="System Configuration" />

      <div className="container py-6">
        <Suspense fallback={
          <div className="flex justify-between items-center mb-6">
            <div className="space-y-2">
              <Skeleton className="h-8 w-[250px]" />
              <Skeleton className="h-4 w-[300px]" />
            </div>
            <Skeleton className="h-10 w-[120px]" />
          </div>
        }>
          <div className="flex justify-between items-center mb-6">
            <div>
              <h1 className="text-2xl font-bold">System Configuration</h1>
              <p className="text-sm text-gray-500 mt-1">
                Configure system-wide settings and preferences
              </p>
            </div>
            <Button>Save Changes</Button>
          </div>
        </Suspense>

        <Suspense fallback={<TabsSkeleton />}>
          <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
            <TabsList className="grid grid-cols-2 lg:grid-cols-4 gap-4">
              <TabsTrigger value="notifications" className="flex items-center gap-2">
                <Bell className="h-4 w-4" />
                Notifications
              </TabsTrigger>
              <TabsTrigger value="email" className="flex items-center gap-2">
                <Mail className="h-4 w-4" />
                Email
              </TabsTrigger>
              <TabsTrigger value="security" className="flex items-center gap-2">
                <Shield className="h-4 w-4" />
                Security
              </TabsTrigger>
              <TabsTrigger value="maintenance" className="flex items-center gap-2">
                <Server className="h-4 w-4" />
                Maintenance
              </TabsTrigger>
            </TabsList>

            <TabsContent value="notifications">
              <Card className="p-6">
                <Suspense fallback={<FormSkeleton />}>
                  <h2 className="text-lg font-semibold mb-4">Notification Settings</h2>
                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Email Notifications</Label>
                        <p className="text-sm text-gray-500">Send notifications via email</p>
                      </div>
                      <Switch
                        checked={systemConfig.notificationSettings.enableEmail}
                        onCheckedChange={(value) =>
                          handleSystemConfigChange("notificationSettings", "enableEmail", value)
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>SMS Notifications</Label>
                        <p className="text-sm text-gray-500">Send notifications via SMS</p>
                      </div>
                      <Switch
                        checked={systemConfig.notificationSettings.enableSMS}
                        onCheckedChange={(value) =>
                          handleSystemConfigChange("notificationSettings", "enableSMS", value)
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>In-App Notifications</Label>
                        <p className="text-sm text-gray-500">Show notifications in the application</p>
                      </div>
                      <Switch
                        checked={systemConfig.notificationSettings.enableInApp}
                        onCheckedChange={(value) =>
                          handleSystemConfigChange("notificationSettings", "enableInApp", value)
                        }
                      />
                    </div>
                  </div>
                </Suspense>
              </Card>
            </TabsContent>

            <TabsContent value="email">
              <Card className="p-6">
                <Suspense fallback={<FormSkeleton />}>
                  <h2 className="text-lg font-semibold mb-4">Email Configuration</h2>
                  <div className="grid gap-4">
                    <div>
                      <Label>SMTP Server</Label>
                      <Input
                        value={systemConfig.emailSettings.smtpServer}
                        onChange={(e) =>
                          handleSystemConfigChange("emailSettings", "smtpServer", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label>Port</Label>
                      <Input
                        type="number"
                        value={systemConfig.emailSettings.port}
                        onChange={(e) =>
                          handleSystemConfigChange("emailSettings", "port", parseInt(e.target.value))
                        }
                      />
                    </div>
                    <div>
                      <Label>Username</Label>
                      <Input
                        value={systemConfig.emailSettings.username}
                        onChange={(e) =>
                          handleSystemConfigChange("emailSettings", "username", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label>Sender Name</Label>
                      <Input
                        value={systemConfig.emailSettings.senderName}
                        onChange={(e) =>
                          handleSystemConfigChange("emailSettings", "senderName", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label>Sender Email</Label>
                      <Input
                        type="email"
                        value={systemConfig.emailSettings.senderEmail}
                        onChange={(e) =>
                          handleSystemConfigChange("emailSettings", "senderEmail", e.target.value)
                        }
                      />
                    </div>
                  </div>
                </Suspense>
              </Card>
            </TabsContent>

            <TabsContent value="security">
              <Card className="p-6">
                <Suspense fallback={<FormSkeleton />}>
                  <h2 className="text-lg font-semibold mb-4">Security Settings</h2>
                  <div className="grid gap-4">
                    <div>
                      <Label>Session Timeout (minutes)</Label>
                      <Input
                        type="number"
                        value={systemConfig.securitySettings.sessionTimeout}
                        onChange={(e) =>
                          handleSystemConfigChange("securitySettings", "sessionTimeout", parseInt(e.target.value))
                        }
                      />
                    </div>
                    <div>
                      <Label>Maximum Login Attempts</Label>
                      <Input
                        type="number"
                        value={systemConfig.securitySettings.maxLoginAttempts}
                        onChange={(e) =>
                          handleSystemConfigChange("securitySettings", "maxLoginAttempts", parseInt(e.target.value))
                        }
                      />
                    </div>
                    <div>
                      <Label>Password Expiry (days)</Label>
                      <Input
                        type="number"
                        value={systemConfig.securitySettings.passwordExpiry}
                        onChange={(e) =>
                          handleSystemConfigChange("securitySettings", "passwordExpiry", parseInt(e.target.value))
                        }
                      />
                    </div>
                    <div className="flex items-center justify-between">
                      <div>
                        <Label>Require Two-Factor Authentication</Label>
                        <p className="text-sm text-gray-500">Enable 2FA for all users</p>
                      </div>
                      <Switch
                        checked={systemConfig.securitySettings.requireTwoFactor}
                        onCheckedChange={(value) =>
                          handleSystemConfigChange("securitySettings", "requireTwoFactor", value)
                        }
                      />
                    </div>
                  </div>
                </Suspense>
              </Card>
            </TabsContent>

            <TabsContent value="maintenance">
              <Card className="p-6">
                <Suspense fallback={<FormSkeleton />}>
                  <h2 className="text-lg font-semibold mb-4">Maintenance Settings</h2>
                  <div className="grid gap-4">
                    <div>
                      <Label>Database Backup Time</Label>
                      <Input
                        type="time"
                        value={systemConfig.maintenanceSettings.backupTime}
                        onChange={(e) =>
                          handleSystemConfigChange("maintenanceSettings", "backupTime", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label>Maintenance Window</Label>
                      <Input
                        value={systemConfig.maintenanceSettings.maintenanceWindow}
                        onChange={(e) =>
                          handleSystemConfigChange("maintenanceSettings", "maintenanceWindow", e.target.value)
                        }
                      />
                    </div>
                    <div>
                      <Label>Data Retention Period (days)</Label>
                      <Input
                        type="number"
                        value={systemConfig.maintenanceSettings.retentionPeriod}
                        onChange={(e) =>
                          handleSystemConfigChange("maintenanceSettings", "retentionPeriod", parseInt(e.target.value))
                        }
                      />
                    </div>
                  </div>
                </Suspense>
              </Card>
            </TabsContent>
          </Tabs>
        </Suspense>
      </div>
    </AppLayout>
  );
} 