import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import AppLayout from '@/layouts/app-layout';
import { type BreadcrumbItem } from '@/types';
import { Head, Link } from '@inertiajs/react';
import { Users, BarChart2, UserCog, FileText, Clock, CheckCircle, UserCheck, Box } from 'lucide-react';
import { stats as userStats } from '@/pages/mswdo-officer/tempData/users';
import { stats as serviceStats } from '@/pages/mswdo-officer/tempData/services';
import { recentActivity, quickStats, serviceMetrics, systemMetrics } from '@/pages/mswdo-officer/tempData/dashboard';
import { Suspense } from 'react';
import { StatCardsSkeleton, CardWithActionsSkeleton, TableRowSkeleton } from '@/components/skeletons/shared-skeletons';

const breadcrumbs: BreadcrumbItem[] = [
    {
        title: 'Dashboard',
        href: '/mswdo-officer/dashboard',
    },
];

export default function MSWDOOfficerDashboard() {
    return (
        <AppLayout breadcrumbs={breadcrumbs}>
            <Head title="MSWDO Officer Dashboard" />

            <div className="flex flex-col gap-6 p-4 md:p-6">
                <div className="flex items-center justify-between">
                    <div>
                        <h1 className="text-2xl md:text-3xl font-bold tracking-tight text-purple-900">MSWDO Officer Dashboard</h1>
                        <p className="text-muted-foreground mt-1 text-sm md:text-base">Municipal Social Welfare and Development Office</p>
                    </div>
                </div>

                {/* System Overview */}
                <Suspense fallback={<StatCardsSkeleton />}>
                    <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-purple-900">Total Users</CardTitle>
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <Users className="h-5 w-5 text-purple-600" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-purple-900">{userStats.totalUsers}</div>
                                <CardDescription className="text-purple-600">
                                    {userStats.verificationStatus.verified} verified users
                                </CardDescription>
                            </CardContent>
                        </Card>

                        <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-purple-900">Active Social Workers</CardTitle>
                                <div className="p-2 bg-purple-100 rounded-lg">
                                    <UserCog className="h-5 w-5 text-purple-600" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-purple-900">{quickStats.activeSocialWorkers}</div>
                                <CardDescription className="text-purple-600">
                                    Currently on duty
                                </CardDescription>
                            </CardContent>
                        </Card>

                        <Card className="border-orange-200 bg-gradient-to-br from-orange-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-orange-900">Pending Applications</CardTitle>
                                <div className="p-2 bg-orange-100 rounded-lg">
                                    <Clock className="h-5 w-5 text-orange-600" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-orange-900">{quickStats.pendingApplications}</div>
                                <CardDescription className="text-orange-600">
                                    Awaiting review
                                </CardDescription>
                            </CardContent>
                        </Card>

                        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-white">
                            <CardHeader className="flex flex-row items-center justify-between pb-3">
                                <CardTitle className="text-sm font-medium text-green-900">System Health</CardTitle>
                                <div className="p-2 bg-green-100 rounded-lg">
                                    <CheckCircle className="h-5 w-5 text-green-600" />
                                </div>
                            </CardHeader>
                            <CardContent>
                                <div className="text-3xl font-bold text-green-900">{systemMetrics.uptime}% Uptime</div>
                                <CardDescription className="text-green-600">
                                    {systemMetrics.responseTime}ms response time
                                </CardDescription>
                            </CardContent>
                        </Card>
                    </div>
                </Suspense>

                {/* Service Statistics */}
                <div className="mt-8">
                    <h2 className="text-2xl font-semibold text-purple-900 mb-6">Service Statistics</h2>
                    <Suspense fallback={<div className="grid gap-6 md:grid-cols-3"><StatCardsSkeleton /></div>}>
                        <div className="grid gap-4 sm:gap-6 grid-cols-1 md:grid-cols-3">
                            <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-white">
                                <CardHeader className="flex flex-row items-center justify-between pb-3">
                                    <CardTitle className="text-sm font-medium text-blue-900">Most Requested Service</CardTitle>
                                    <div className="p-2 bg-blue-100 rounded-lg">
                                        <FileText className="h-5 w-5 text-blue-600" />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-blue-900">{serviceMetrics.mostRequested[0].name}</div>
                                    <CardDescription className="text-blue-600">
                                        {serviceMetrics.mostRequested[0].applications} applications
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className="border-indigo-200 bg-gradient-to-br from-indigo-50 to-white">
                                <CardHeader className="flex flex-row items-center justify-between pb-3">
                                    <CardTitle className="text-sm font-medium text-indigo-900">Today's Appointments</CardTitle>
                                    <div className="p-2 bg-indigo-100 rounded-lg">
                                        <CheckCircle className="h-5 w-5 text-indigo-600" />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-indigo-900">{quickStats.todayAppointments}</div>
                                    <CardDescription className="text-indigo-600">
                                        Scheduled appointments
                                    </CardDescription>
                                </CardContent>
                            </Card>

                            <Card className="border-slate-200 bg-gradient-to-br from-slate-50 to-white">
                                <CardHeader className="flex flex-row items-center justify-between pb-3">
                                    <CardTitle className="text-sm font-medium text-slate-900">Storage Used</CardTitle>
                                    <div className="p-2 bg-slate-100 rounded-lg">
                                        <Database className="h-5 w-5 text-slate-600" />
                                    </div>
                                </CardHeader>
                                <CardContent>
                                    <div className="text-2xl font-bold text-slate-900">{systemMetrics.storageUsed}GB</div>
                                    <CardDescription className="text-slate-600">
                                        Last backup: {systemMetrics.lastBackup}
                                    </CardDescription>
                                </CardContent>
                            </Card>
                        </div>
                    </Suspense>
                </div>

                {/* Recent Activity */}
                <div className="mt-8">
                    <h2 className="text-2xl font-semibold text-purple-900 mb-6">Recent System Activity</h2>
                    <Suspense fallback={
                        <Card className="border-purple-200">
                            <CardContent className="p-0">
                                <div className="divide-y divide-purple-100">
                                    {[...Array(5)].map((_, i) => (
                                        <TableRowSkeleton key={i} />
                                    ))}
                                </div>
                            </CardContent>
                        </Card>
                    }>
                        <Card className="border-purple-200 bg-gradient-to-br from-purple-50/30 to-white">
                            <CardContent className="p-0">
                                <div className="divide-y divide-purple-100">
                                    {recentActivity.map((activity, index) => (
                                        <div key={index} className="flex items-center justify-between p-6 hover:bg-purple-50/50 transition-colors">
                                            <div className="flex items-start space-x-4">
                                                <div className="p-2 bg-purple-100 rounded-lg">
                                                    <BarChart2 className="h-4 w-4 text-purple-600" />
                                                </div>
                                                <div className="space-y-1">
                                                    <p className="text-sm font-medium leading-none text-purple-900">{activity.description}</p>
                                                    <p className="text-sm text-purple-600">{activity.details}</p>
                                                </div>
                                            </div>
                                            <div className="text-sm text-purple-500 font-medium">{activity.time}</div>
                                        </div>
                                ))}
                            </div>
                        </CardContent>
                    </Card>
                </Suspense>
                </div>

                {/* Quick Actions */}
                <div className="mt-8">
                    <h2 className="text-2xl font-semibold text-purple-900 mb-6">Administrative Actions</h2>
                    <Suspense fallback={<div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">{[...Array(4)].map((_, i) => <CardWithActionsSkeleton key={i} />)}</div>}>
                        <div className="grid gap-4 sm:gap-6 grid-cols-1 sm:grid-cols-2 lg:grid-cols-4">
                            <Link href="/mswdo-officer/beneficiaries">
                                <Card className="cursor-pointer hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 border-purple-200 bg-gradient-to-br from-purple-50/30 to-white group">
                                    <CardContent className="flex flex-col items-center justify-center p-8">
                                        <div className="p-4 bg-purple-100 rounded-xl mb-4 group-hover:bg-purple-200 transition-colors">
                                            <Users className="h-8 w-8 text-purple-600" />
                                        </div>
                                        <h3 className="font-semibold text-purple-900 text-center">Beneficiary Management</h3>
                                        <p className="text-sm text-purple-600 text-center mt-2">Manage welfare beneficiaries</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href="/mswdo-officer/cases">
                                <Card className="cursor-pointer hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 border-purple-200 bg-gradient-to-br from-purple-50/30 to-white group">
                                    <CardContent className="flex flex-col items-center justify-center p-8">
                                        <div className="p-4 bg-purple-100 rounded-xl mb-4 group-hover:bg-purple-200 transition-colors">
                                            <FileText className="h-8 w-8 text-purple-600" />
                                        </div>
                                        <h3 className="font-semibold text-purple-900 text-center">Case Management</h3>
                                        <p className="text-sm text-purple-600 text-center mt-2">Track welfare cases</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href="/mswdo-officer/services">
                                <Card className="cursor-pointer hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 border-purple-200 bg-gradient-to-br from-purple-50/30 to-white group">
                                    <CardContent className="flex flex-col items-center justify-center p-8">
                                        <div className="p-4 bg-purple-100 rounded-xl mb-4 group-hover:bg-purple-200 transition-colors">
                                            <Box className="h-8 w-8 text-purple-600" />
                                        </div>
                                        <h3 className="font-semibold text-purple-900 text-center">Service Programs</h3>
                                        <p className="text-sm text-purple-600 text-center mt-2">Manage social services</p>
                                    </CardContent>
                                </Card>
                            </Link>

                            <Link href="/mswdo-officer/staff">
                                <Card className="cursor-pointer hover:bg-purple-50 hover:border-purple-300 transition-all duration-300 border-purple-200 bg-gradient-to-br from-purple-50/30 to-white group">
                                    <CardContent className="flex flex-col items-center justify-center p-8">
                                        <div className="p-4 bg-purple-100 rounded-xl mb-4 group-hover:bg-purple-200 transition-colors">
                                            <UserCheck className="h-8 w-8 text-purple-600" />
                                        </div>
                                        <h3 className="font-semibold text-purple-900 text-center">Staff Management</h3>
                                        <p className="text-sm text-purple-600 text-center mt-2">Manage MSWDO staff</p>
                                    </CardContent>
                                </Card>
                            </Link>
                        </div>
                    </Suspense>
                </div>
            </div>
        </AppLayout>
    );
}