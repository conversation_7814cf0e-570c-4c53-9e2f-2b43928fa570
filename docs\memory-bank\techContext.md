# Technical Context

## Technology Stack
- **Framework**: <PERSON><PERSON> (PHP)
- **Frontend**: React with TypeScript
- **Data Layer**: Inertia.js
- **UI Components**: Custom React components
- **Styling**: Tailwind CSS

## Development Setup
The project follows a modern Laravel + React setup with Inertia.js integration:
- <PERSON><PERSON> handles backend routing and data management
- React components are used for frontend UI
- Inertia.js bridges <PERSON><PERSON> and <PERSON>act for seamless SPA experience
- TypeScript ensures type safety across the frontend

## Key Technical Patterns
1. **Component Structure**
   - Page components in `resources/js/pages/`
   - Reusable UI components
   - Role-based layouts (Admin, Client, Superadmin)

2. **Data Management**
   - Inertia.js for data passing between <PERSON><PERSON> and <PERSON>act
   - React state management for UI interactions
   - Type definitions for data structures

3. **UI/UX Implementation**
   - Responsive design using Tailwind CSS
   - Card-based layouts for content organization
   - Tab-based navigation for different application states
   - Filter and search functionality

## Type Definitions
Key interfaces identified:
- `Auth` - Authentication related types
- `BreadcrumbItem` - Navigation structure
- `User` - User data structure

## Component Architecture
### Admin Section
- Applications management (`applications.tsx`)
  - Filtering by status, service, priority
  - Search functionality
  - Application card display
  - Notes management
  - Status updates

## Dependencies
- `@inertiajs/react` - Inertia.js React adapter
- `lucide-react` - Icon components
- Custom UI components for consistent design

## Technical Constraints
- Must maintain type safety with TypeScript
- Follow Laravel + Inertia.js patterns
- Ensure responsive design
- Implement role-based access control

## Development Setup

### Project Setup
```bash
# Install PHP dependencies
composer install

# Install frontend dependencies
npm install

# Start development server
php artisan serve

# In another terminal, run Vite development server
npm run dev

# Build for production
npm run build
```

### Running Locally
To run the Balagtas SocialCare system locally:

1. Ensure PHP and Composer are installed
2. Ensure Node.js and npm are installed
3. Clone the repository
4. Run `composer install` to install PHP dependencies
5. Run `npm install` to install frontend dependencies
6. Copy `.env.example` to `.env` and configure the environment
7. Run `php artisan key:generate` to generate an application key
8. Run `php artisan migrate` to set up the database
9. Run `php artisan serve` to start the Laravel server
10. In a separate terminal, run `npm run dev` to start the Vite development server
11. Access the application at http://127.0.0.1:8000

### Environment Configuration
- Environment variables managed via .env files
- Laravel configuration via config directory
- Database configured to use SQLite for local development

## Technical Constraints

### Browser Support
- Modern browsers (Chrome, Firefox, Safari, Edge)
- No support for IE11 or older browsers

### Performance Requirements
- Initial load time < 3 seconds on broadband
- Time to interactive < 5 seconds
- Responsive design for various screen sizes (desktop-first approach)

### Security Requirements
- Secure authentication (leveraging Laravel's auth system)
- Data encryption for sensitive information
- CSRF protection (built into Laravel)
- Input validation and sanitization
- Role-based access control

### Accessibility Requirements
- WCAG 2.1 AA compliance (target)
- Screen reader compatibility
- Keyboard navigation support
- Sufficient color contrast

## Technical Dependencies

### Frontend Dependencies
- React 19.0.0
- React DOM 19.0.0
- TypeScript 5.8.2
- Tailwind CSS 4.0.10
- Tailwind CSS Animate 1.0.7
- Inertia.js for React 2.0.4
- Lucide React 0.475.0 for icons
- Headless UI components
- Radix UI components:
  - Dialog 1.1.6
  - Dropdown Menu 2.1.6
  - Label 2.1.2
  - Separator 1.1.2
  - Checkbox 1.1.4
  - Avatar 1.1.3
  - Select 2.1.6
  - Tooltip 1.1.8
- class-variance-authority 0.7.1
- clsx 2.1.1 and tailwind-merge 3.0.2 for conditional classes

### Development Dependencies
- Vite 6.2.0
- Laravel Vite Plugin 1.2.0
- ESLint 9.21.0
- Prettier 3.5.3
- TypeScript 5.8.2
- @types/react 19.0.10 and @types/react-dom 19.0.4

## Implemented UI Components

### Core Components
- **App Layout**: Main application layout with sidebar, header, and content area
- **Sidebar**: Navigation sidebar with role-specific sections
- **Dashboard Cards**: Status indicator cards with counts and icons
- **Recent Activity**: Timeline component for recent actions
- **Document Upload**: File upload interface with drag-and-drop
- **Multi-Step Form**: Progressive form for BFACES application
- **Service Cards**: Visual service listing with apply buttons
- **Application Tracking**: Status tracking interface with timeline
- **Admin Verification Queue**: List view of verification requests
- **Admin Applications**: Management interface for applications

### Pages
- **Client Dashboard**: Overview page with status cards and recent activity
- **Residency Verification**: Document upload for residency proof
- **BFACES Application**: Multi-step form for emergency assistance
- **Services**: Available social services catalog
- **Applications**: Client application tracking
- **Admin Dashboard**: Overview for social workers
- **Admin Verifications**: Queue for residency verification
- **Admin Applications**: Service applications management
- **Admin Interviews**: Interview scheduling and management
- **Admin Reports**: Reporting and analytics (placeholder)

## API Structure

### Authentication Endpoints
- Already implemented via Laravel Breeze:
  - POST /register - User registration
  - POST /login - User authentication
  - POST /logout - User logout
  - GET /user - Get authenticated user

### User Profile Endpoints (To Implement)
- GET /profile - Get user profile
- PUT /profile - Update user profile
- POST /profile/documents - Upload profile documents

### Residency Verification Endpoints (To Implement)
- POST /residency/verify - Submit residency verification
- GET /residency/status - Check verification status

### BFACES Endpoints (To Implement)
- POST /bfaces/application - Submit BFACES application
- GET /bfaces/status - Check BFACES status

### Service Endpoints (To Implement)
- GET /services - List available services
- GET /services/{id} - Get service details
- POST /services/{id}/apply - Apply for a service
- GET /services/applications - List user's service applications
- GET /services/applications/{id} - Get application details

### Admin Endpoints (To Implement)
- GET /admin/verifications - List residency verifications
- PUT /admin/verifications/{id} - Update verification status
- GET /admin/applications - List service applications
- PUT /admin/applications/{id} - Update application status

## Database Schemas (To Implement)

### Core Tables
- users: Basic user authentication and role data (already provided by Laravel)
- profiles: Extended user profile information
- residency_verifications: Residency verification requests and status
- bfaces_applications: BFACES application data
- services: Available service types and configurations
- service_applications: Service request tracking
- documents: Uploaded file metadata
- interviews: Appointment scheduling and tracking
- notifications: System notifications and alerts

## Frontend Architecture

### Core Technologies
- **React**: Frontend library for building user interfaces
- **TypeScript**: Static typing for improved development experience
- **Tailwind CSS**: Utility-first CSS framework for styling
- **shadcn/ui**: Component library built on Radix UI primitives
- **Lucide Icons**: Icon library for consistent visual elements

### State Management
- **React Query**: Data fetching and caching
- **useForm Hook**: Form state management with validation
- **React Context**: Global state management where needed
- **Local State**: Component-level state using useState/useReducer

### Admin Interface Components

1. **Layout Components**
   - `AppLayout`: Base layout with navigation and breadcrumbs
   - `Head`: Page metadata and title management
   - `Card`: Container component for content sections
   - `Button`: Action triggers with variants
   - `Badge`: Status and label display
   - `Input`: Form input fields
   - `Label`: Form field labels
   - `Tabs`: Content organization
   - `Dialog`: Modal dialogs for actions/confirmations

2. **Form Components**
   - Dynamic list management
   - File upload handling
   - Rich text editing
   - Date/time selection
   - Multi-select dropdowns
   - Search inputs with debouncing
   - Filter components

3. **Data Display**
   - Paginated lists
   - Sortable tables
   - Statistics cards
   - Status indicators
   - Timeline displays
   - Document previews

### API Integration

1. **Endpoints**
   ```typescript
   // Case Management
   GET    /api/cases
   POST   /api/cases
   GET    /api/cases/:id
   PUT    /api/cases/:id
   DELETE /api/cases/:id
   
   // Services Management
   GET    /api/services
   POST   /api/services
   GET    /api/services/:id
   PUT    /api/services/:id
   DELETE /api/services/:id
   
   // Document Verification
   GET    /api/verifications
   POST   /api/verifications/:id/verify
   POST   /api/verifications/:id/reject
   
   // BFACES Applications
   GET    /api/bfaces
   GET    /api/bfaces/:id
   POST   /api/bfaces/:id/review
   POST   /api/bfaces/:id/approve
   POST   /api/bfaces/:id/reject
   ```

2. **Response Types**
   ```typescript
   interface PaginatedResponse<T> {
     data: T[]
     meta: {
       current_page: number
       last_page: number
       per_page: number
       total: number
     }
   }
   
   interface ApiError {
     message: string
     errors?: Record<string, string[]>
   }
   ```

### Performance Optimizations

1. **Data Loading**
   - Pagination for large datasets
   - Infinite scroll where appropriate
   - Debounced search inputs
   - Optimistic updates for better UX
   - Prefetching for common navigation paths

2. **Component Optimization**
   - Memoization of expensive computations
   - Virtual scrolling for long lists
   - Lazy loading of heavy components
   - Image optimization and lazy loading
   - Code splitting by route

3. **Caching Strategy**
   - React Query cache configuration
   - Stale-while-revalidate pattern
   - Cache invalidation on mutations
   - Prefetching of likely data

### Security Measures

1. **Authentication**
   - JWT token management
   - Role-based access control
   - Session management
   - Secure cookie handling

2. **Data Protection**
   - Input sanitization
   - XSS prevention
   - CSRF protection
   - Rate limiting
   - Sensitive data handling

### Error Handling

1. **Client-Side**
   ```typescript
   try {
     const response = await api.post(endpoint, data)
     // Handle success
   } catch (error) {
     if (error instanceof ApiError) {
       // Handle known API errors
     } else {
       // Handle unexpected errors
     }
   }
   ```

2. **Error Boundaries**
   ```typescript
   class ErrorBoundary extends React.Component {
     state = { hasError: false }
     
     static getDerivedStateFromError() {
       return { hasError: true }
     }
     
     render() {
       if (this.state.hasError) {
         return <ErrorFallback />
       }
       return this.props.children
     }
   }
   ```

### Testing Strategy

1. **Unit Tests**
   - Component testing with React Testing Library
   - Hook testing
   - Utility function testing
   - Mock service workers for API testing

2. **Integration Tests**
   - Page-level tests
   - User flow testing
   - API integration testing
   - Error handling testing

3. **E2E Tests**
   - Critical path testing
   - User journey testing
   - Cross-browser testing
   - Performance testing

### Development Tools

1. **Code Quality**
   - ESLint configuration
   - Prettier formatting
   - TypeScript strict mode
   - Husky pre-commit hooks

2. **Build Tools**
   - Vite for development
   - PostCSS processing
   - Asset optimization
   - Bundle analysis

3. **Monitoring**
   - Error tracking
   - Performance monitoring
   - Usage analytics
   - API metrics

## Authentication & Middleware

### RedirectBasedOnRole Middleware
- Location: `app/Http/Middleware/RedirectBasedOnRole.php`
- Purpose: Handles role-based redirection after login
- Key Features:
  - Role-specific landing page routing
  - Intended URL preservation
  - Session-based redirection logic
  - Integration with Laravel's authentication system

### Kernel Registration
```php
protected $middlewareAliases = [
    'auth' => \App\Http\Middleware\Authenticate::class,
    'redirect.role' => \App\Http\Middleware\RedirectBasedOnRole::class,
    // ... other middleware
];
```

### Role-Based Routes
- Admin routes: `/admin/*` - Protected by admin middleware
- Client routes: `/client/*` - Protected by client middleware
- Superadmin routes: `/superadmin/*` - Protected by superadmin middleware
- Public routes: Accessible without authentication

## Security Implementation

### Authentication
- Laravel Breeze for authentication scaffolding
- Session-based authentication
- Remember-me functionality
- Password reset capabilities
- Email verification

### Authorization
- Role-based middleware guards
- Policy-based access control
- Route protection
- Resource authorization

### Security Headers
- CSRF protection
- XSS prevention
- Content Security Policy
- Frame options
- SSL/TLS enforcement

## Authentication & Authorization

### Dependencies
- Laravel Sanctum for API token authentication
- Laravel Fortify for authentication scaffolding
- Laravel Breeze for frontend authentication views
- Laravel Spatie Permissions for role management

### Configuration
```php
// config/auth.php
'guards' => [
    'web' => [
        'driver' => 'session',
        'provider' => 'users',
    ],
    'api' => [
        'driver' => 'sanctum',
        'provider' => 'users',
    ],
],
```

### Key Files
- `app/Http/Middleware/Authenticate.php` - Base auth middleware
- `app/Http/Middleware/RoleMiddleware.php` - Role checks
- `app/Models/User.php` - User model with role relations
- `database/migrations/*_create_users_table.php` - User schema
- `routes/auth.php` - Authentication routes

### Security Measures
- Password hashing using bcrypt
- Rate limiting on login attempts
- Session fixation protection
- XSS protection headers
- SQL injection prevention
- CSRF token validation

## Development Approach
The project follows a frontend-first development strategy:

### Frontend-First Philosophy
- Focus on building complete UI/UX before backend implementation
- Use of temporary data structures and mock responses
- Emphasis on user experience and interaction patterns
- Backend schema and API design driven by frontend needs

### Current Implementation State
- Frontend: Comprehensive UI implementation with temporary data
- Backend: Basic Laravel setup with authentication only
- Database: Default Laravel migrations for users and roles
- API: To be designed based on frontend requirements

### Development Workflow
1. Build and validate UI components and flows
2. Implement interactions with temporary data
3. Document data requirements from UI implementation
4. Design API endpoints based on UI needs
5. Implement backend features iteratively
6. Replace temporary data with real backend integration

// ... existing code ... 