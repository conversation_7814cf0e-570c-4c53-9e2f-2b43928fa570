import { usePage } from '@inertiajs/react';

interface DebugProps {
    user_data: {
        id: number;
        name: string;
        email: string;
        role: string;
        normalized_role: string;
        role_length: number;
        role_bytes: string;
    } | null;
}

export default function DebugFrontend() {
    const { user_data, auth } = usePage<DebugProps & { auth: any }>().props;

    function normalizeRole(role: string): string {
        if (!role) return 'applicant';
        
        const roleMap: Record<string, string> = {
            'superadmin': 'mswdo-officer',
            'admin': 'mswdo-officer',
            'mswdo-officer': 'mswdo-officer',
            'MSWDO Officer': 'mswdo-officer',
            'social-worker': 'social-worker',
            'Social Worker': 'social-worker',
            'applicant': 'applicant',
            'client': 'applicant',
            'Applicant': 'applicant',
        };
        
        return roleMap[role.trim()] || 'applicant';
    }

    const frontendNormalizedRole = auth?.user?.role ? normalizeRole(auth.user.role) : 'unknown';

    return (
        <div className="p-8">
            <h1 className="text-2xl font-bold mb-6">Frontend Debug Information</h1>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div className="bg-gray-100 p-4 rounded">
                    <h2 className="text-lg font-semibold mb-3">Backend User Data</h2>
                    <pre className="text-sm">{JSON.stringify(user_data, null, 2)}</pre>
                </div>
                
                <div className="bg-blue-100 p-4 rounded">
                    <h2 className="text-lg font-semibold mb-3">Frontend Auth Data</h2>
                    <pre className="text-sm">{JSON.stringify(auth, null, 2)}</pre>
                </div>
                
                <div className="bg-green-100 p-4 rounded">
                    <h2 className="text-lg font-semibold mb-3">Frontend Role Processing</h2>
                    <div className="text-sm">
                        <p><strong>Raw Role:</strong> {auth?.user?.role || 'undefined'}</p>
                        <p><strong>Frontend Normalized:</strong> {frontendNormalizedRole}</p>
                        <p><strong>Backend Normalized:</strong> {user_data?.normalized_role || 'undefined'}</p>
                    </div>
                </div>
                
                <div className="bg-yellow-100 p-4 rounded">
                    <h2 className="text-lg font-semibold mb-3">Navigation Logic Test</h2>
                    <div className="text-sm">
                        <p><strong>Should show nav for:</strong> {frontendNormalizedRole}</p>
                        <p><strong>Expected nav items:</strong></p>
                        <ul className="list-disc list-inside mt-2">
                            {frontendNormalizedRole === 'mswdo-officer' && (
                                <>
                                    <li>Dashboard</li>
                                    <li>System Configuration</li>
                                    <li>Service Management</li>
                                    <li>User Management</li>
                                    <li>Budget Allocation</li>
                                    <li>Database Management</li>
                                    <li>Reports & Analytics</li>
                                </>
                            )}
                            {frontendNormalizedRole === 'social-worker' && (
                                <>
                                    <li>Dashboard</li>
                                    <li>Verifications</li>
                                    <li>Applications</li>
                                    <li>Interviews</li>
                                    <li>Clients</li>
                                    <li>Reports</li>
                                </>
                            )}
                            {frontendNormalizedRole === 'applicant' && (
                                <>
                                    <li>Dashboard</li>
                                    <li>Residency Verification</li>
                                    <li>BFACES Application</li>
                                    <li>Services</li>
                                    <li>Applications</li>
                                    <li>Appointments</li>
                                    <li>Documents</li>
                                </>
                            )}
                        </ul>
                    </div>
                </div>
            </div>
        </div>
    );
}
